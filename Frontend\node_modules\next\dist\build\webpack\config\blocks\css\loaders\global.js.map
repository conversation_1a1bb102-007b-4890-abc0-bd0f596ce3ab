{"version": 3, "sources": ["../../../../../../../src/build/webpack/config/blocks/css/loaders/global.ts"], "names": ["getGlobalCssLoader", "ctx", "postcss", "preProcessors", "loaders", "isClient", "push", "getClientStyleLoader", "hasAppDir", "isAppDir", "isDevelopment", "assetPrefix", "loader", "require", "resolve", "options", "importLoaders", "length", "modules", "url", "resourcePath", "cssFileResolve", "experimental", "urlImports", "import", "_", "slice", "reverse"], "mappings": ";;;;+BAMgBA;;;eAAAA;;;wBAHqB;6BACN;AAExB,SAASA,mBACdC,GAAyB,EACzBC,OAAY,EACZC,gBAAmD,EAAE;IAErD,MAAMC,UAAoC,EAAE;IAE5C,IAAIH,IAAII,QAAQ,EAAE;QAChB,4DAA4D;QAC5D,SAAS;QACTD,QAAQE,IAAI,CACVC,IAAAA,4BAAoB,EAAC;YACnBC,WAAWP,IAAIO,SAAS;YACxBC,UAAUR,IAAIQ,QAAQ;YACtBC,eAAeT,IAAIS,aAAa;YAChCC,aAAaV,IAAIU,WAAW;QAC9B;IAEJ;IAEA,sCAAsC;IACtCP,QAAQE,IAAI,CAAC;QACXM,QAAQC,QAAQC,OAAO,CAAC;QACxBC,SAAS;YACPb;YACAc,eAAe,IAAIb,cAAcc,MAAM;YACvC,4CAA4C;YAC5CC,SAAS;YACTC,KAAK,CAACA,KAAaC,eACjBC,IAAAA,2BAAc,EAACF,KAAKC,cAAcnB,IAAIqB,YAAY,CAACC,UAAU;YAC/DC,QAAQ,CAACL,KAAaM,GAAQL,eAC5BC,IAAAA,2BAAc,EAACF,KAAKC,cAAcnB,IAAIqB,YAAY,CAACC,UAAU;QACjE;IACF;IAEA,cAAc;IACdnB,QAAQE,IAAI,CAAC;QACXM,QAAQC,QAAQC,OAAO,CAAC;QACxBC,SAAS;YACPb;QACF;IACF;IAEAE,QAAQE,IAAI,CACV,sEAAsE;IACtE,0BAA0B;OACvBH,cAAcuB,KAAK,GAAGC,OAAO;IAGlC,OAAOvB;AACT"}