{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/internal/container/BuildError.tsx"], "names": ["React", "Dialog", "DialogBody", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogHeader", "Overlay", "Terminal", "VersionStalenessInfo", "noop", "css", "BuildError", "message", "versionInfo", "useCallback", "fixed", "type", "aria-<PERSON>by", "aria-<PERSON><PERSON>", "onClose", "className", "h4", "id", "content", "footer", "p", "small", "styles"], "mappings": ";;;;;;;;;;;AAAA,YAAYA,WAAW,QAAO;AAE9B,SACEC,MAAM,EACNC,UAAU,EACVC,aAAa,EACbC,YAAY,QACP,uBAAsB;AAC7B,SAASC,OAAO,QAAQ,wBAAuB;AAC/C,SAASC,QAAQ,QAAQ,yBAAwB;AACjD,SAASC,oBAAoB,QAAQ,qCAAoC;AACzE,SAASC,QAAQC,GAAG,QAAQ,2BAA0B;AAItD,OAAO,MAAMC,aAAwC,SAASA,WAAW,KAGxE;IAHwE,IAAA,EACvEC,OAAO,EACPC,WAAW,EACZ,GAHwE;IAIvE,MAAMJ,OAAOR,MAAMa,WAAW,CAAC,KAAO,GAAG,EAAE;IAC3C,qBACE,KAACR;QAAQS,KAAK;kBACZ,cAAA,KAACb;YACCc,MAAK;YACLC,mBAAgB;YAChBC,oBAAiB;YACjBC,SAASV;sBAET,cAAA,MAACL;;kCACC,MAACC;wBAAae,WAAU;;0CACtB,KAACC;gCAAGC,IAAG;0CAAsC;;4BAC5CT,4BAAc,KAACL;gCAAsB,GAAGK,WAAW;iCAAO;;;kCAE7D,MAACV;wBAAWiB,WAAU;;0CACpB,KAACb;gCAASgB,SAASX;;0CACnB,KAACY;0CACC,cAAA,KAACC;oCAAEH,IAAG;8CACJ,cAAA,KAACI;kDAAM;;;;;;;;;;AAWvB,EAAC;AAED,OAAO,MAAMC,SAASjB,uBAqBrB"}