{"version": 3, "sources": ["../../../../../src/shared/lib/router/utils/add-path-prefix.ts"], "names": ["addPathPrefix", "path", "prefix", "startsWith", "pathname", "query", "hash", "parsePath"], "mappings": ";;;;+BAMgBA;;;eAAAA;;;2BANU;AAMnB,SAASA,cAAcC,IAAY,EAAEC,MAAe;IACzD,IAAI,CAACD,KAAKE,UAAU,CAAC,QAAQ,CAACD,QAAQ;QACpC,OAAOD;IACT;IAEA,MAAM,EAAEG,QAAQ,EAAEC,KAAK,EAAEC,IAAI,EAAE,GAAGC,IAAAA,oBAAS,EAACN;IAC5C,OAAO,AAAC,KAAEC,SAASE,WAAWC,QAAQC;AACxC"}