{"version": 3, "sources": ["../../../../../src/shared/lib/router/utils/add-path-suffix.ts"], "names": ["addPathSuffix", "path", "suffix", "startsWith", "pathname", "query", "hash", "parsePath"], "mappings": ";;;;+BAOgBA;;;eAAAA;;;2BAPU;AAOnB,SAASA,cAAcC,IAAY,EAAEC,MAAe;IACzD,IAAI,CAACD,KAAKE,UAAU,CAAC,QAAQ,CAACD,QAAQ;QACpC,OAAOD;IACT;IAEA,MAAM,EAAEG,QAAQ,EAAEC,KAAK,EAAEC,IAAI,EAAE,GAAGC,IAAAA,oBAAS,EAACN;IAC5C,OAAO,AAAC,KAAEG,WAAWF,SAASG,QAAQC;AACxC"}