{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/internal/container/RuntimeError/index.tsx"], "names": ["styles", "RuntimeError", "error", "firstFirstPartyFrameIndex", "React", "useMemo", "frames", "findIndex", "entry", "expanded", "Boolean", "originalCodeFrame", "originalStackFrame", "firstFrame", "allLeadingFrames", "slice", "all", "setAll", "useState", "toggleAll", "useCallback", "v", "leading<PERSON>ram<PERSON>", "filter", "f", "allCallStackFrames", "visibleCallStackFrames", "canShowMore", "length", "stackFramesGroupedByFramework", "groupStackFramesByFramework", "Fragment", "h2", "map", "frame", "index", "CallStackFrame", "CodeFrame", "stackFrame", "codeFrame", "undefined", "componentStackFrames", "componentStackFrame", "ComponentStackFrameRow", "GroupedStackFrames", "groupedStackFrames", "button", "tabIndex", "data-nextjs-data-runtime-error-collapsed-action", "type", "onClick", "css"], "mappings": ";;;;;;;;;;;;;;;IA6HaA,MAAM;eAANA;;IAwFJC,YAAY;eAAZA;;;;;;iEArNc;2BACG;8BAEE;6CAEgB;gCACb;oCACI;wCACI;;;;;;;;;;AAIvC,MAAMA,eAA4C,SAASA,aAAa,KAEvE;IAFuE,IAAA,EACtEC,KAAK,EACN,GAFuE;IAGtE,MAAMC,4BAA4BC,OAAMC,OAAO,CAAS;QACtD,OAAOH,MAAMI,MAAM,CAACC,SAAS,CAC3B,CAACC,QACCA,MAAMC,QAAQ,IACdC,QAAQF,MAAMG,iBAAiB,KAC/BD,QAAQF,MAAMI,kBAAkB;IAEtC,GAAG;QAACV,MAAMI,MAAM;KAAC;IACjB,MAAMO,aAAaT,OAAMC,OAAO,CAA4B;YACnDH;QAAP,OAAOA,CAAAA,0CAAAA,MAAMI,MAAM,CAACH,0BAA0B,YAAvCD,0CAA2C;IACpD,GAAG;QAACA,MAAMI,MAAM;QAAEH;KAA0B;IAE5C,MAAMW,mBAAmBV,OAAMC,OAAO,CACpC,IACEF,4BAA4B,IACxB,EAAE,GACFD,MAAMI,MAAM,CAACS,KAAK,CAAC,GAAGZ,4BAC5B;QAACD,MAAMI,MAAM;QAAEH;KAA0B;IAG3C,MAAM,CAACa,KAAKC,OAAO,GAAGb,OAAMc,QAAQ,CAACL,cAAc;IACnD,MAAMM,YAAYf,OAAMgB,WAAW,CAAC;QAClCH,OAAO,CAACI,IAAM,CAACA;IACjB,GAAG,EAAE;IAEL,MAAMC,gBAAgBlB,OAAMC,OAAO,CACjC,IAAMS,iBAAiBS,MAAM,CAAC,CAACC,IAAMA,EAAEf,QAAQ,IAAIO,MACnD;QAACA;QAAKF;KAAiB;IAEzB,MAAMW,qBAAqBrB,OAAMC,OAAO,CACtC,IAAMH,MAAMI,MAAM,CAACS,KAAK,CAACZ,4BAA4B,IACrD;QAACD,MAAMI,MAAM;QAAEH;KAA0B;IAE3C,MAAMuB,yBAAyBtB,OAAMC,OAAO,CAC1C,IAAMoB,mBAAmBF,MAAM,CAAC,CAACC,IAAMA,EAAEf,QAAQ,IAAIO,MACrD;QAACA;QAAKS;KAAmB;IAG3B,MAAME,cAAcvB,OAAMC,OAAO,CAAU;QACzC,OACEoB,mBAAmBG,MAAM,KAAKF,uBAAuBE,MAAM,IAC1DZ,OAAOH,cAAc;IAE1B,GAAG;QACDG;QACAS,mBAAmBG,MAAM;QACzBf;QACAa,uBAAuBE,MAAM;KAC9B;IAED,MAAMC,gCAAgCzB,OAAMC,OAAO,CACjD,IAAMyB,IAAAA,wDAA2B,EAACJ,yBAClC;QAACA;KAAuB;IAG1B,qBACE,sBAACtB,OAAM2B,QAAQ;;YACZlB,2BACC,sBAACT,OAAM2B,QAAQ;;kCACb,qBAACC;kCAAG;;oBACHV,cAAcW,GAAG,CAAC,CAACC,OAAOC,sBACzB,qBAACC,8BAAc;4BAEbF,OAAOA;2BADF,AAAC,mBAAgBC,QAAM,MAAGnB;kCAInC,qBAACqB,oBAAS;wBACRC,YAAYzB,WAAWD,kBAAkB;wBACzC2B,WAAW1B,WAAWF,iBAAiB;;;iBAGzC6B;YAEHtC,MAAMuC,oBAAoB,iBACzB;;kCACE,qBAACT;kCAAG;;oBACH9B,MAAMuC,oBAAoB,CAACR,GAAG,CAAC,CAACS,qBAAqBP,sBACpD,qBAACQ,8CAAsB;4BAErBD,qBAAqBA;2BADhBP;;iBAKT;YAEHN,8BAA8BD,MAAM,iBACnC,sBAACxB,OAAM2B,QAAQ;;kCACb,qBAACC;kCAAG;;kCACJ,qBAACY,sCAAkB;wBACjBC,oBAAoBhB;wBACpBb,KAAKA;;;iBAGPwB;YACHb,4BACC,qBAACvB,OAAM2B,QAAQ;0BACb,cAAA,sBAACe;oBACCC,UAAU;oBACVC,iDAA+C;oBAC/CC,MAAK;oBACLC,SAAS/B;;wBAERH,MAAM,SAAS;wBAAO;;;iBAGzBwB;;;AAGV;AAEO,MAAMxC,aAASmD,kBAAG"}