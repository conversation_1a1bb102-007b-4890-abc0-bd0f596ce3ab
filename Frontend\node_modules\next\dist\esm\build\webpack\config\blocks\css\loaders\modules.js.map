{"version": 3, "sources": ["../../../../../../../src/build/webpack/config/blocks/css/loaders/modules.ts"], "names": ["getClientStyleLoader", "cssFileResolve", "getCssModuleLocalIdent", "getCssModuleLoader", "ctx", "postcss", "preProcessors", "loaders", "isClient", "push", "hasAppDir", "isAppDir", "isDevelopment", "assetPrefix", "loader", "require", "resolve", "options", "importLoaders", "length", "esModule", "url", "resourcePath", "experimental", "urlImports", "import", "_", "modules", "exportLocalsConvention", "exportOnlyLocals", "isServer", "mode", "getLocalIdent", "slice", "reverse"], "mappings": "AAEA,SAASA,oBAAoB,QAAQ,WAAU;AAC/C,SAASC,cAAc,QAAQ,iBAAgB;AAC/C,SAASC,sBAAsB,QAAQ,2BAA0B;AAEjE,OAAO,SAASC,mBACdC,GAAyB,EACzBC,OAAY,EACZC,gBAAmD,EAAE;IAErD,MAAMC,UAAoC,EAAE;IAE5C,IAAIH,IAAII,QAAQ,EAAE;QAChB,4DAA4D;QAC5D,SAAS;QACTD,QAAQE,IAAI,CACVT,qBAAqB;YACnBU,WAAWN,IAAIM,SAAS;YACxBC,UAAUP,IAAIO,QAAQ;YACtBC,eAAeR,IAAIQ,aAAa;YAChCC,aAAaT,IAAIS,WAAW;QAC9B;IAEJ;IAEA,sCAAsC;IACtCN,QAAQE,IAAI,CAAC;QACXK,QAAQC,QAAQC,OAAO,CAAC;QACxBC,SAAS;YACPZ;YACAa,eAAe,IAAIZ,cAAca,MAAM;YACvC,4CAA4C;YAC5CC,UAAU;YACVC,KAAK,CAACA,KAAaC,eACjBrB,eAAeoB,KAAKC,cAAclB,IAAImB,YAAY,CAACC,UAAU;YAC/DC,QAAQ,CAACJ,KAAaK,GAAQJ,eAC5BrB,eAAeoB,KAAKC,cAAclB,IAAImB,YAAY,CAACC,UAAU;YAC/DG,SAAS;gBACP,mEAAmE;gBACnEC,wBAAwB;gBACxB,2CAA2C;gBAC3CC,kBAAkBzB,IAAI0B,QAAQ;gBAC9B,6DAA6D;gBAC7D,iCAAiC;gBACjCC,MAAM;gBACN,oDAAoD;gBACpD,uDAAuD;gBACvD,eAAe;gBACf,2DAA2D;gBAC3D,aAAa;gBACbC,eAAe9B;YACjB;QACF;IACF;IAEA,cAAc;IACdK,QAAQE,IAAI,CAAC;QACXK,QAAQC,QAAQC,OAAO,CAAC;QACxBC,SAAS;YACPZ;QACF;IACF;IAEAE,QAAQE,IAAI,CACV,sEAAsE;IACtE,0BAA0B;OACvBH,cAAc2B,KAAK,GAAGC,OAAO;IAGlC,OAAO3B;AACT"}