{"version": 3, "sources": ["../../../../../src/shared/lib/router/utils/get-next-pathname-info.ts"], "names": ["normalizeLocalePath", "removePathPrefix", "pathHasPrefix", "getNextPathnameInfo", "pathname", "options", "basePath", "i18n", "trailingSlash", "nextConfig", "info", "endsWith", "pathnameNoDataPrefix", "startsWith", "paths", "replace", "split", "buildId", "slice", "join", "parseData", "result", "i18nProvider", "analyze", "locales", "locale", "detectedLocale"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,mCAAkC;AACtE,SAASC,gBAAgB,QAAQ,uBAAsB;AACvD,SAASC,aAAa,QAAQ,oBAAmB;AAkDjD,OAAO,SAASC,oBACdC,QAAgB,EAChBC,OAAgB;QAE0BA;IAA1C,MAAM,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,aAAa,EAAE,GAAGH,CAAAA,sBAAAA,QAAQI,UAAU,YAAlBJ,sBAAsB,CAAC;IACjE,MAAMK,OAAyB;QAC7BN;QACAI,eAAeJ,aAAa,MAAMA,SAASO,QAAQ,CAAC,OAAOH;IAC7D;IAEA,IAAIF,YAAYJ,cAAcQ,KAAKN,QAAQ,EAAEE,WAAW;QACtDI,KAAKN,QAAQ,GAAGH,iBAAiBS,KAAKN,QAAQ,EAAEE;QAChDI,KAAKJ,QAAQ,GAAGA;IAClB;IACA,IAAIM,uBAAuBF,KAAKN,QAAQ;IAExC,IACEM,KAAKN,QAAQ,CAACS,UAAU,CAAC,mBACzBH,KAAKN,QAAQ,CAACO,QAAQ,CAAC,UACvB;QACA,MAAMG,QAAQJ,KAAKN,QAAQ,CACxBW,OAAO,CAAC,oBAAoB,IAC5BA,OAAO,CAAC,WAAW,IACnBC,KAAK,CAAC;QAET,MAAMC,UAAUH,KAAK,CAAC,EAAE;QACxBJ,KAAKO,OAAO,GAAGA;QACfL,uBACEE,KAAK,CAAC,EAAE,KAAK,UAAU,AAAC,MAAGA,MAAMI,KAAK,CAAC,GAAGC,IAAI,CAAC,OAAS;QAE1D,sDAAsD;QACtD,kDAAkD;QAClD,IAAId,QAAQe,SAAS,KAAK,MAAM;YAC9BV,KAAKN,QAAQ,GAAGQ;QAClB;IACF;IAEA,4EAA4E;IAC5E,yBAAyB;IACzB,IAAIL,MAAM;QACR,IAAIc,SAAShB,QAAQiB,YAAY,GAC7BjB,QAAQiB,YAAY,CAACC,OAAO,CAACb,KAAKN,QAAQ,IAC1CJ,oBAAoBU,KAAKN,QAAQ,EAAEG,KAAKiB,OAAO;QAEnDd,KAAKe,MAAM,GAAGJ,OAAOK,cAAc;YACnBL;QAAhBX,KAAKN,QAAQ,GAAGiB,CAAAA,mBAAAA,OAAOjB,QAAQ,YAAfiB,mBAAmBX,KAAKN,QAAQ;QAEhD,IAAI,CAACiB,OAAOK,cAAc,IAAIhB,KAAKO,OAAO,EAAE;YAC1CI,SAAShB,QAAQiB,YAAY,GACzBjB,QAAQiB,YAAY,CAACC,OAAO,CAACX,wBAC7BZ,oBAAoBY,sBAAsBL,KAAKiB,OAAO;YAE1D,IAAIH,OAAOK,cAAc,EAAE;gBACzBhB,KAAKe,MAAM,GAAGJ,OAAOK,cAAc;YACrC;QACF;IACF;IACA,OAAOhB;AACT"}