{"version": 3, "sources": ["../../../../../../src/build/webpack/loaders/postcss-loader/src/index.ts"], "names": ["Warning", "SyntaxError", "normalizeSourceMap", "normalizeSourceMapAfterPostcss", "loader", "content", "sourceMap", "meta", "loaderSpan", "currentTraceSpan", "<PERSON><PERSON><PERSON><PERSON>", "callback", "async", "traceAsyncFn", "options", "getOptions", "file", "resourcePath", "useSourceMap", "processOptions", "from", "to", "map", "inline", "annotation", "prev", "traceFn", "context", "root", "ast", "type", "setAttribute", "postcssWithPlugins", "postcss", "result", "process", "error", "addDependency", "name", "warning", "warnings", "emitWarning", "message", "messages", "addBuildDependency", "addMissingDependency", "addContextDependency", "dir", "emitFile", "info", "toJSON", "undefined", "version", "processor", "css", "then", "err"], "mappings": "AAAA,OAAOA,aAAa,YAAW;AAC/B,OAAOC,iBAAiB,UAAS;AACjC,SAASC,kBAAkB,EAAEC,8BAA8B,QAAQ,UAAS;AAE5E;;;;CAIC,GACD,eAAe,eAAeC,OAE5B,WAAW,GACXC,OAAe,EACf,eAAe,GACfC,SAAc,EACdC,IAAS;IAET,MAAMC,aAAa,IAAI,CAACC,gBAAgB,CAACC,UAAU,CAAC;IACpD,MAAMC,WAAW,IAAI,CAACC,KAAK;IAE3BJ,WACGK,YAAY,CAAC;QACZ,MAAMC,UAAU,IAAI,CAACC,UAAU;QAC/B,MAAMC,OAAO,IAAI,CAACC,YAAY;QAE9B,MAAMC,eACJ,OAAOJ,QAAQR,SAAS,KAAK,cACzBQ,QAAQR,SAAS,GACjB,IAAI,CAACA,SAAS;QAEpB,MAAMa,iBAAsB;YAC1BC,MAAMJ;YACNK,IAAIL;QACN;QAEA,IAAIE,cAAc;YAChBC,eAAeG,GAAG,GAAG;gBACnBC,QAAQ;gBACRC,YAAY;gBACZ,GAAGL,eAAeG,GAAG;YACvB;QACF;QAEA,IAAIhB,aAAaa,eAAeG,GAAG,EAAE;YACnCH,eAAeG,GAAG,CAACG,IAAI,GAAGjB,WACvBE,UAAU,CAAC,wBACXgB,OAAO,CAAC,IAAMxB,mBAAmBI,WAAW,IAAI,CAACqB,OAAO;QAC7D;QAEA,IAAIC;QAEJ,uCAAuC;QACvC,IAAIrB,QAAQA,KAAKsB,GAAG,IAAItB,KAAKsB,GAAG,CAACC,IAAI,KAAK,WAAW;YACjD,CAAA,EAAEF,IAAI,EAAE,GAAGrB,KAAKsB,GAAG,AAAD;YACpBrB,WAAWuB,YAAY,CAAC,WAAW;QACrC;QAEA,mCAAmC;QACnC,MAAM,EAAEC,kBAAkB,EAAE,GAAG,MAAMlB,QAAQmB,OAAO;QAEpD,IAAIC;QAEJ,IAAI;YACFA,SAAS,MAAM1B,WACZE,UAAU,CAAC,mBACXG,YAAY,CAAC,IACZmB,mBAAmBG,OAAO,CAACP,QAAQvB,SAASc;QAElD,EAAE,OAAOiB,OAAY;YACnB,IAAIA,MAAMpB,IAAI,EAAE;gBACd,IAAI,CAACqB,aAAa,CAACD,MAAMpB,IAAI;YAC/B;YAEA,IAAIoB,MAAME,IAAI,KAAK,kBAAkB;gBACnC,MAAM,IAAIrC,YAAYmC;YACxB;YAEA,MAAMA;QACR;QAEA,KAAK,MAAMG,WAAWL,OAAOM,QAAQ,GAAI;YACvC,IAAI,CAACC,WAAW,CAAC,IAAIzC,QAAQuC;QAC/B;QAEA,KAAK,MAAMG,WAAWR,OAAOS,QAAQ,CAAE;YACrC,wCAAwC;YACxC,OAAQD,QAAQZ,IAAI;gBAClB,KAAK;oBACH,IAAI,CAACO,aAAa,CAACK,QAAQ1B,IAAI;oBAC/B;gBACF,KAAK;oBACH,IAAI,CAAC4B,kBAAkB,CAACF,QAAQ1B,IAAI;oBACpC;gBACF,KAAK;oBACH,IAAI,CAAC6B,oBAAoB,CAACH,QAAQ1B,IAAI;oBACtC;gBACF,KAAK;oBACH,IAAI,CAAC8B,oBAAoB,CAACJ,QAAQ1B,IAAI;oBACtC;gBACF,KAAK;oBACH,IAAI,CAAC8B,oBAAoB,CAACJ,QAAQK,GAAG;oBACrC;gBACF,KAAK;oBACH,IAAIL,QAAQrC,OAAO,IAAIqC,QAAQ1B,IAAI,EAAE;wBACnC,IAAI,CAACgC,QAAQ,CACXN,QAAQ1B,IAAI,EACZ0B,QAAQrC,OAAO,EACfqC,QAAQpC,SAAS,EACjBoC,QAAQO,IAAI;oBAEhB;YACJ;QACF;QAEA,wCAAwC;QACxC,IAAI3B,MAAMY,OAAOZ,GAAG,GAAGY,OAAOZ,GAAG,CAAC4B,MAAM,KAAKC;QAE7C,IAAI7B,OAAOJ,cAAc;YACvBI,MAAMnB,+BAA+BmB,KAAK,IAAI,CAACK,OAAO;QACxD;QAEA,MAAME,MAAM;YACVC,MAAM;YACNsB,SAASlB,OAAOmB,SAAS,CAACD,OAAO;YACjCxB,MAAMM,OAAON,IAAI;QACnB;QAEA,OAAO;YAACM,OAAOoB,GAAG;YAAEhC;YAAK;gBAAEO;YAAI;SAAE;IACnC,GACC0B,IAAI,CACH,CAAC,CAACD,KAAKhC,KAAK,EAAEO,GAAG,EAAE,CAAM;QACvBlB,4BAAAA,SAAW,MAAM2C,KAAKhC,KAAK;YAAEO;QAAI;IACnC,GACA,CAAC2B;QACC7C,4BAAAA,SAAW6C;IACb;AAEN"}