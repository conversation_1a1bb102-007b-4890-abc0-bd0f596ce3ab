/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(paginas)/dashboard/page",{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CUSUARIO%5COneDrive%5CDocumentos%5CSpringBoot%5CServicios%5CFrontend%5Csrc%5Capp%5C(paginas)%5Cdashboard%5Cpage.tsx&server=false!":
/*!*************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CUSUARIO%5COneDrive%5CDocumentos%5CSpringBoot%5CServicios%5CFrontend%5Csrc%5Capp%5C(paginas)%5Cdashboard%5Cpage.tsx&server=false! ***!
  \*************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./src/app/(paginas)/dashboard/page.tsx */ \"(app-pages-browser)/./src/app/(paginas)/dashboard/page.tsx\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz1DJTNBJTVDVXNlcnMlNUNVU1VBUklPJTVDT25lRHJpdmUlNUNEb2N1bWVudG9zJTVDU3ByaW5nQm9vdCU1Q1NlcnZpY2lvcyU1Q0Zyb250ZW5kJTVDc3JjJTVDYXBwJTVDKHBhZ2luYXMpJTVDZGFzaGJvYXJkJTVDcGFnZS50c3gmc2VydmVyPWZhbHNlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvPzE3OTIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxVU1VBUklPXFxcXE9uZURyaXZlXFxcXERvY3VtZW50b3NcXFxcU3ByaW5nQm9vdFxcXFxTZXJ2aWNpb3NcXFxcRnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFwocGFnaW5hcylcXFxcZGFzaGJvYXJkXFxcXHBhZ2UudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CUSUARIO%5COneDrive%5CDocumentos%5CSpringBoot%5CServicios%5CFrontend%5Csrc%5Capp%5C(paginas)%5Cdashboard%5Cpage.tsx&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/(paginas)/dashboard/page.tsx":
/*!**********************************************!*\
  !*** ./src/app/(paginas)/dashboard/page.tsx ***!
  \**********************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// \"use client\";\n// import React, { useEffect, useState } from \"react\";\n// import { Grid, Card, Typography, Box, Skeleton } from \"@mui/material\";\n// import { Timer } from \"@mui/icons-material\";\n// import MetricCard from \"../../components/cards/MetricCard\";\n// import WeatherWidget from \"../../components/weather/WeatherWidget\";\n// import FarmSummary from \"../../components/farm/FarmSummary\";\n// import TaskList from \"../../components/tasks/TaskList\";\n// const Dashboard = () => {\n//   // Estados para el formulario de eventos y visualización\n//   const [selectedDate, setSelectedDate] = useState<string>(\"\");\n//   const [showEventForm, setShowEventForm] = useState(false);\n//   const [showEvents, setShowEvents] = useState(false);\n//   const [events, setEvents] = useState<\n//     Array<{\n//       id: string;\n//       date: string;\n//       title: string;\n//       description: string;\n//       time: string;\n//     }>\n//   >([]);\n//   // Activar el estado de loading\n//   const [loading, setLoading] = useState(true);\n//   // Efecto para simular la carga de datos\n//   useEffect(() => {\n//     const fetchData = async () => {\n//       setLoading(true);\n//       try {\n//         await new Promise((resolve) => setTimeout(resolve, 1500));\n//       } catch (error) {\n//         console.error(\"Error fetching data:\", error);\n//       } finally {\n//         setLoading(false);\n//       }\n//     };\n//     fetchData();\n//   }, []);\n//   // Función para manejar la selección de fecha\n//   const handleDateClick = (date: string) => {\n//     setSelectedDate(date);\n//     setShowEventForm(true);\n//     setShowEvents(false);\n//   };\n//   const formatearFecha = (fecha: Date) => {\n//     const fechaFormateada = fecha.toLocaleDateString(\"es-ES\", {\n//       weekday: \"long\",\n//       day: \"numeric\",\n//       month: \"long\",\n//       year: \"numeric\",\n//     });\n//     // Dividir la cadena en palabras\n//     const palabras = fechaFormateada.split(\" \");\n//     // Capitalizar la primera letra del día y del mes\n//     palabras[0] = palabras[0].charAt(0).toUpperCase() + palabras[0].slice(1); // día de la semana\n//     palabras[3] = palabras[3].charAt(0).toUpperCase() + palabras[3].slice(1); // mes\n//     // Unir las palabras de nuevo\n//     return palabras.join(\" \");\n//   };\n//   // Añadir esta función para determinar la estación actual\n//   const obtenerEstacionActual = () => {\n//     const fecha = new Date();\n//     const mes = fecha.getMonth() + 1; // getMonth() devuelve 0-11\n//     const dia = fecha.getDate();\n//     // Verano: 21 de diciembre - 20 de marzo\n//     if ((mes === 12 && dia >= 21) || mes <= 2 || (mes === 3 && dia <= 20)) {\n//       return \"Verano\";\n//     }\n//     // Otoño: 21 de marzo - 20 de junio\n//     else if ((mes === 3 && dia >= 21) || mes <= 5 || (mes === 6 && dia <= 20)) {\n//       return \"Otoño\";\n//     }\n//     // Invierno: 21 de junio - 20 de septiembre\n//     else if ((mes === 6 && dia >= 21) || mes <= 8 || (mes === 9 && dia <= 20)) {\n//       return \"Invierno\";\n//     }\n//     // Primavera: 21 de septiembre - 20 de diciembre\n//     else {\n//       return \"Primavera\";\n//     }\n//   };\n//   // Función para determinar el ciclo agrícola\n//   const obtenerCicloAgricola = () => {\n//     const estacion = obtenerEstacionActual();\n//     return estacion === \"Otoño\" || estacion === \"Invierno\"\n//       ? \"Otoño-Invierno\"\n//       : \"Primavera-Verano\";\n//   };\n//   // Función para formatear la fecha actual\n//   const formatearFechaActual = () => {\n//     const fecha = new Date();\n//     const fechaFormateada = fecha.toLocaleDateString(\"es-ES\", {\n//       weekday: \"long\",\n//       day: \"numeric\",\n//       month: \"long\",\n//     });\n//     // Dividir la cadena en palabras y capitalizar\n//     const palabras = fechaFormateada.split(\" \");\n//     palabras[0] = palabras[0].charAt(0).toUpperCase() + palabras[0].slice(1); // día de la semana\n//     palabras[3] = palabras[3].charAt(0).toUpperCase() + palabras[3].slice(1); // mes\n//     return palabras.join(\" \");\n//   };\n//   return (\n//     <Box sx={{ padding: \"16px\" }}>\n//       {/* Header Section */}\n//       <Box sx={{ mb: 4 }}>\n//         <Box\n//           sx={{\n//             display: \"flex\",\n//             flexDirection: \"column\",\n//             mb: 3,\n//           }}\n//         >\n//           <Typography\n//             variant=\"h6\"\n//             fontWeight=\"bold\"\n//             sx={{\n//               color: \"#2E7D32\",\n//               fontFamily: \"Lexend, sans-serif\",\n//               fontSize: { xs: \"1.3rem\", sm: \"1.6rem\", md: \"1.9rem\" },\n//               lineHeight: 1.2,\n//               whiteSpace: \"nowrap\",\n//               mb: 1,\n//             }}\n//           >\n//             Bienvenido al Dashboard Agropecuario\n//           </Typography>\n//           <Typography\n//             variant=\"subtitle1\"\n//             sx={{\n//               color: \"#666\",\n//               fontFamily: \"Inter\",\n//               fontSize: { xs: \"0.9rem\", sm: \"1rem\", md: \"1.1rem\" },\n//               lineHeight: 1.3,\n//             }}\n//           >\n//             Gestiona sus Servicios de forma inteligente y eficiente\n//           </Typography>\n//         </Box>\n//       </Box>\n//       <Grid container spacing={3}>\n//         {/* Sección de Cards */}\n//         <Grid item xs={12}>\n//           <Grid container spacing={3}>\n//             <Grid item xs={12} sm={6} md={3}>\n//               <MetricCard\n//                 title=\"Total de Lotes\"\n//                 value={\"\"}\n//                 change=\"+5% desde ayer\"\n//                 icon=\"carduno.png\"\n//                 loading={loading}\n//                 bgColor={\"\"}\n//                 hoverColor={\"\"}\n//               />\n//             </Grid>\n//             <Grid item xs={12} sm={6} md={3}>\n//               <MetricCard\n//                 title=\"Área Total\"\n//                 value={\"\"}\n//                 change=\"+3% desde la semana pasada\"\n//                 icon=\"carddos.png\"\n//                 loading={loading}\n//                 bgColor={\"\"}\n//                 hoverColor={\"\"}\n//               />\n//             </Grid>\n//             <Grid item xs={12} sm={6} md={3}>\n//               <MetricCard\n//                 title=\"Total de Parcelas\"\n//                 value={\"\"}\n//                 change=\"-2% desde el trimestre pasado\"\n//                 icon=\"cardtres.png\"\n//                 loading={loading}\n//                 bgColor={\"\"}\n//                 hoverColor={\"\"}\n//               />\n//             </Grid>\n//             <Grid item xs={12} sm={6} md={3}>\n//               {loading ? (\n//                 <Card\n//                   sx={{\n//                     backgroundColor: \"#f8fafc\",\n//                     borderRadius: \"16px\",\n//                     boxShadow: \"0px 4px 12px rgba(0, 0, 0, 0.1)\",\n//                     padding: \"16px\",\n//                   }}\n//                 >\n//                   <Box\n//                     display=\"flex\"\n//                     justifyContent=\"space-between\"\n//                     alignItems=\"center\"\n//                     mb={2}\n//                   >\n//                     <Skeleton variant=\"text\" width=\"50%\" height={24} />\n//                     <Skeleton variant=\"circular\" width={40} height={40} />\n//                   </Box>\n//                   <Skeleton variant=\"text\" width=\"70%\" height={48} />\n//                   <Skeleton\n//                     variant=\"text\"\n//                     width=\"40%\"\n//                     height={24}\n//                     sx={{ mt: 1 }}\n//                   />\n//                 </Card>\n//               ) : (\n//                 <Card\n//                   sx={{\n//                     padding: (theme) => theme.spacing(2),\n//                     backgroundColor: \"#ffffff\",\n//                     borderRadius: \"8px\",\n//                     border: \"1px solid #E5E7EB\",\n//                     boxShadow: \"2px 2px 0px rgba(31, 142, 235, 0.2)\",\n//                     marginBottom: (theme) => theme.spacing(2),\n//                     transition: \"all 0.2s ease\",\n//                     \"&:hover\": {\n//                       transform: \"translate(-1px, -1px)\",\n//                       boxShadow: \"3px 3px 0px rgba(31, 142, 235, 0.3)\",\n//                     },\n//                   }}\n//                 >\n//                   <Box\n//                     display=\"flex\"\n//                     justifyContent=\"space-between\"\n//                     alignItems=\"center\"\n//                     mb={2}\n//                   >\n//                     <Typography\n//                       variant=\"body1\"\n//                       fontWeight=\"bold\"\n//                       sx={{\n//                         color: \"#000000\",\n//                         fontFamily: \"Lexend, sans-serif\",\n//                       }}\n//                     >\n//                       Temporada Actual\n//                     </Typography>\n//                     <Box\n//                       sx={{\n//                         borderRadius: \"50%\",\n//                         width: \"40px\",\n//                         height: \"40px\",\n//                         display: \"flex\",\n//                         justifyContent: \"center\",\n//                         alignItems: \"center\",\n//                       }}\n//                     >\n//                       <Timer sx={{ color: \"#2196F3\", fontSize: \"24px\" }} />\n//                     </Box>\n//                   </Box>\n//                   <Typography\n//                     variant=\"h4\"\n//                     fontWeight=\"bold\"\n//                     sx={{ color: \"#000000\", fontFamily: \"Inter\" }}\n//                   >\n//                     {obtenerEstacionActual()}\n//                   </Typography>\n//                   <Typography\n//                     variant=\"body2\"\n//                     sx={{\n//                       color: \"#666666\",\n//                       fontWeight: \"600\",\n//                       mt: 1,\n//                     }}\n//                   >\n//                     {obtenerCicloAgricola()}\n//                   </Typography>\n//                   <Typography\n//                     variant=\"body2\"\n//                     sx={{\n//                       color: \"#888888\",\n//                       fontWeight: \"500\",\n//                       mt: 0.5,\n//                       fontSize: \"0.85rem\",\n//                       fontFamily: \"Inter\",\n//                     }}\n//                   >\n//                     {formatearFechaActual()}\n//                   </Typography>\n//                 </Card>\n//               )}\n//             </Grid>\n//           </Grid>\n//         </Grid>\n//         {/* Sección de Farm Summary y Weather Widget */}\n//         <Grid item xs={12}>\n//           <Grid container spacing={3}>\n//             <Grid item xs={12} md={8}>\n//               {loading ? (\n//                 <Box\n//                   sx={{\n//                     bgcolor: \"#F1F8E9\",\n//                     p: 2,\n//                     borderRadius: 2,\n//                     border: \"1px solid #C5E1A5\",\n//                     minHeight: \"200px\", // Altura mínima fija\n//                     maxHeight: \"fit-content\", // Altura máxima adaptable\n//                     display: \"flex\",\n//                     flexDirection: \"column\",\n//                   }}\n//                 >\n//                   {/* Header skeleton */}\n//                   <Box\n//                     sx={{\n//                       display: \"flex\",\n//                       justifyContent: \"space-between\",\n//                       pb: 2,\n//                       borderBottom: \"1px solid #e0e0e0\",\n//                     }}\n//                   >\n//                     <Skeleton variant=\"text\" width=\"40%\" height={32} />\n//                     <Skeleton variant=\"text\" width=\"15%\" height={24} />\n//                   </Box>\n//                   {/* Cards grid skeleton */}\n//                   <Box sx={{ mt: 2, flex: 1 }}>\n//                     <Grid container spacing={2}>\n//                       {[1, 2, 3, 4, 5, 6].map((item) => (\n//                         <Grid item xs={12} sm={6} md={4} key={item}>\n//                           <Box\n//                             sx={{\n//                               p: 2,\n//                               bgcolor: \"#f8fafc\",\n//                               borderRadius: 2,\n//                               height: \"100px\", // Altura fija para cada card\n//                             }}\n//                           >\n//                             <Skeleton variant=\"text\" width=\"80%\" height={24} />\n//                             <Skeleton\n//                               variant=\"text\"\n//                               width=\"60%\"\n//                               height={20}\n//                               sx={{ mt: 1 }}\n//                             />\n//                             <Skeleton\n//                               variant=\"text\"\n//                               width=\"70%\"\n//                               height={20}\n//                               sx={{ mt: 1 }}\n//                             />\n//                           </Box>\n//                         </Grid>\n//                       ))}\n//                     </Grid>\n//                   </Box>\n//                 </Box>\n//               ) : (\n//                 <FarmSummary />\n//               )}\n//             </Grid>\n//             <Grid item xs={12} md={4}>\n//               {loading ? (\n//                 <Box\n//                   sx={{\n//                     bgcolor: \"#F1F8E9\",\n//                     p: 2,\n//                     borderRadius: 2,\n//                     border: \"1px solid #C5E1A5\",\n//                     height: \"100%\",\n//                     maxHeight: \"400px\", // Altura máxima fija\n//                   }}\n//                 >\n//                   <Skeleton variant=\"text\" width=\"60%\" height={24} />\n//                   <Skeleton variant=\"rectangular\" height={200} sx={{ mt: 2 }} />\n//                   <Box sx={{ mt: 2 }}>\n//                     <Skeleton variant=\"text\" width=\"40%\" height={20} />\n//                     <Skeleton variant=\"text\" width=\"60%\" height={20} />\n//                     <Skeleton variant=\"text\" width=\"80%\" height={20} />\n//                   </Box>\n//                 </Box>\n//               ) : (\n//                 <WeatherWidget />\n//               )}\n//             </Grid>\n//           </Grid>\n//         </Grid>\n//         {/* Sección de TaskList */}\n//         <Grid item xs={12}>\n//           <Grid container spacing={3}>\n//             <Grid item xs={12} md={8}>\n//               {loading ? (\n//                 <Box\n//                   sx={{\n//                     bgcolor: \"#F1F8E9\",\n//                     p: 2,\n//                     borderRadius: 2,\n//                     border: \"1px solid #C5E1A5\",\n//                     height: \"100%\",\n//                   }}\n//                 >\n//                   {/* Header skeleton */}\n//                   <Box\n//                     sx={{\n//                       display: \"flex\",\n//                       justifyContent: \"space-between\",\n//                       pb: 2,\n//                       borderBottom: \"1px solid #e0e0e0\",\n//                     }}\n//                   >\n//                     <Skeleton variant=\"text\" width=\"40%\" height={32} />\n//                     <Skeleton variant=\"text\" width=\"15%\" height={24} />\n//                   </Box>\n//                   {/* Tasks skeleton */}\n//                   <Box sx={{ mt: 2 }}>\n//                     {[1, 2, 3, 4, 5].map((item) => (\n//                       <Box\n//                         key={item}\n//                         sx={{\n//                           mb: 2,\n//                           p: 2,\n//                           bgcolor: \"#f8fafc\",\n//                           borderRadius: 2,\n//                         }}\n//                       >\n//                         <Box\n//                           sx={{\n//                             display: \"flex\",\n//                             justifyContent: \"space-between\",\n//                             alignItems: \"flex-start\",\n//                           }}\n//                         >\n//                           <Box sx={{ display: \"flex\", gap: 1, width: \"70%\" }}>\n//                             <Skeleton\n//                               variant=\"circular\"\n//                               width={24}\n//                               height={24}\n//                             />\n//                             <Box sx={{ flex: 1 }}>\n//                               <Skeleton\n//                                 variant=\"text\"\n//                                 width=\"80%\"\n//                                 height={24}\n//                               />\n//                               <Skeleton\n//                                 variant=\"text\"\n//                                 width=\"60%\"\n//                                 height={20}\n//                               />\n//                             </Box>\n//                           </Box>\n//                           <Box\n//                             sx={{\n//                               display: \"flex\",\n//                               flexDirection: \"column\",\n//                               alignItems: \"flex-end\",\n//                               gap: 1,\n//                             }}\n//                           >\n//                             <Skeleton\n//                               variant=\"rectangular\"\n//                               width={80}\n//                               height={24}\n//                               sx={{ borderRadius: 1 }}\n//                             />\n//                             <Skeleton variant=\"text\" width={100} height={20} />\n//                           </Box>\n//                         </Box>\n//                       </Box>\n//                     ))}\n//                   </Box>\n//                 </Box>\n//               ) : (\n//                 <TaskList limit={5} />\n//               )}\n//             </Grid>\n//           </Grid>\n//         </Grid>\n//       </Grid>\n//     </Box>\n//   );\n// };\n// export default Dashboard;\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(paginas)/dashboard/page.tsx\n"));

/***/ })

});