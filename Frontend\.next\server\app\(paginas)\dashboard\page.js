/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/(paginas)/dashboard/page";
exports.ids = ["app/(paginas)/dashboard/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F(paginas)%2Fdashboard%2Fpage&page=%2F(paginas)%2Fdashboard%2Fpage&appPaths=%2F(paginas)%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2F(paginas)%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5CUSUARIO%5COneDrive%5CDocumentos%5CSpringBoot%5CServicios%5CFrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUSUARIO%5COneDrive%5CDocumentos%5CSpringBoot%5CServicios%5CFrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F(paginas)%2Fdashboard%2Fpage&page=%2F(paginas)%2Fdashboard%2Fpage&appPaths=%2F(paginas)%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2F(paginas)%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5CUSUARIO%5COneDrive%5CDocumentos%5CSpringBoot%5CServicios%5CFrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUSUARIO%5COneDrive%5CDocumentos%5CSpringBoot%5CServicios%5CFrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '(paginas)',\n        {\n        children: [\n        'dashboard',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(paginas)/dashboard/page.tsx */ \"(rsc)/./src/app/(paginas)/dashboard/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(paginas)/layout.tsx */ \"(rsc)/./src/app/(paginas)/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/(paginas)/dashboard/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/(paginas)/dashboard/page\",\n        pathname: \"/dashboard\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F(paginas)%2Fdashboard%2Fpage&page=%2F(paginas)%2Fdashboard%2Fpage&appPaths=%2F(paginas)%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2F(paginas)%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5CUSUARIO%5COneDrive%5CDocumentos%5CSpringBoot%5CServicios%5CFrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUSUARIO%5COneDrive%5CDocumentos%5CSpringBoot%5CServicios%5CFrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CUSUARIO%5COneDrive%5CDocumentos%5CSpringBoot%5CServicios%5CFrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CUSUARIO%5COneDrive%5CDocumentos%5CSpringBoot%5CServicios%5CFrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CUSUARIO%5COneDrive%5CDocumentos%5CSpringBoot%5CServicios%5CFrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CUSUARIO%5COneDrive%5CDocumentos%5CSpringBoot%5CServicios%5CFrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CUSUARIO%5COneDrive%5CDocumentos%5CSpringBoot%5CServicios%5CFrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CUSUARIO%5COneDrive%5CDocumentos%5CSpringBoot%5CServicios%5CFrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CUSUARIO%5COneDrive%5CDocumentos%5CSpringBoot%5CServicios%5CFrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CUSUARIO%5COneDrive%5CDocumentos%5CSpringBoot%5CServicios%5CFrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CUSUARIO%5COneDrive%5CDocumentos%5CSpringBoot%5CServicios%5CFrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CUSUARIO%5COneDrive%5CDocumentos%5CSpringBoot%5CServicios%5CFrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CUSUARIO%5COneDrive%5CDocumentos%5CSpringBoot%5CServicios%5CFrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CUSUARIO%5COneDrive%5CDocumentos%5CSpringBoot%5CServicios%5CFrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CUSUARIO%5COneDrive%5CDocumentos%5CSpringBoot%5CServicios%5CFrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CUSUARIO%5COneDrive%5CDocumentos%5CSpringBoot%5CServicios%5CFrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CUSUARIO%5COneDrive%5CDocumentos%5CSpringBoot%5CServicios%5CFrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CUSUARIO%5COneDrive%5CDocumentos%5CSpringBoot%5CServicios%5CFrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CUSUARIO%5COneDrive%5CDocumentos%5CSpringBoot%5CServicios%5CFrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CUSUARIO%5COneDrive%5CDocumentos%5CSpringBoot%5CServicios%5CFrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CUSUARIO%5COneDrive%5CDocumentos%5CSpringBoot%5CServicios%5CFrontend%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5C(paginas)%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5CUSUARIO%5COneDrive%5CDocumentos%5CSpringBoot%5CServicios%5CFrontend%5Csrc%5Capp%5Ccomponents%5Cmenu%5CMenuPrincipal.tsx&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CUSUARIO%5COneDrive%5CDocumentos%5CSpringBoot%5CServicios%5CFrontend%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5C(paginas)%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5CUSUARIO%5COneDrive%5CDocumentos%5CSpringBoot%5CServicios%5CFrontend%5Csrc%5Capp%5Ccomponents%5Cmenu%5CMenuPrincipal.tsx&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/components/menu/MenuPrincipal.tsx */ \"(ssr)/./src/app/components/menu/MenuPrincipal.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDVVNVQVJJTyU1Q09uZURyaXZlJTVDRG9jdW1lbnRvcyU1Q1NwcmluZ0Jvb3QlNUNTZXJ2aWNpb3MlNUNGcm9udGVuZCU1Q25vZGVfbW9kdWxlcyU1Q25leHQlNUNmb250JTVDZ29vZ2xlJTVDdGFyZ2V0LmNzcyUzRiU3QiUyMnBhdGglMjIlM0ElMjJzcmMlNUMlNUNhcHAlNUMlNUMocGFnaW5hcyklNUMlNUNsYXlvdXQudHN4JTIyJTJDJTIyaW1wb3J0JTIyJTNBJTIySW50ZXIlMjIlMkMlMjJhcmd1bWVudHMlMjIlM0ElNUIlN0IlMjJzdWJzZXRzJTIyJTNBJTVCJTIybGF0aW4lMjIlNUQlN0QlNUQlMkMlMjJ2YXJpYWJsZU5hbWUlMjIlM0ElMjJpbnRlciUyMiU3RCZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q1VTVUFSSU8lNUNPbmVEcml2ZSU1Q0RvY3VtZW50b3MlNUNTcHJpbmdCb290JTVDU2VydmljaW9zJTVDRnJvbnRlbmQlNUNzcmMlNUNhcHAlNUNjb21wb25lbnRzJTVDbWVudSU1Q01lbnVQcmluY2lwYWwudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL2Zyb250ZW5kLz9iOWFmIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcVVNVQVJJT1xcXFxPbmVEcml2ZVxcXFxEb2N1bWVudG9zXFxcXFNwcmluZ0Jvb3RcXFxcU2VydmljaW9zXFxcXEZyb250ZW5kXFxcXHNyY1xcXFxhcHBcXFxcY29tcG9uZW50c1xcXFxtZW51XFxcXE1lbnVQcmluY2lwYWwudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CUSUARIO%5COneDrive%5CDocumentos%5CSpringBoot%5CServicios%5CFrontend%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5C(paginas)%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5CUSUARIO%5COneDrive%5CDocumentos%5CSpringBoot%5CServicios%5CFrontend%5Csrc%5Capp%5Ccomponents%5Cmenu%5CMenuPrincipal.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CUSUARIO%5COneDrive%5CDocumentos%5CSpringBoot%5CServicios%5CFrontend%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5CUSUARIO%5COneDrive%5CDocumentos%5CSpringBoot%5CServicios%5CFrontend%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Lexend%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22lexend%22%7D&modules=C%3A%5CUsers%5CUSUARIO%5COneDrive%5CDocumentos%5CSpringBoot%5CServicios%5CFrontend%5Csrc%5Capp%5Ccomponents%5CThemeProviderWrapper.tsx&modules=C%3A%5CUsers%5CUSUARIO%5COneDrive%5CDocumentos%5CSpringBoot%5CServicios%5CFrontend%5Csrc%5Capp%5Cglobals.css&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CUSUARIO%5COneDrive%5CDocumentos%5CSpringBoot%5CServicios%5CFrontend%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5CUSUARIO%5COneDrive%5CDocumentos%5CSpringBoot%5CServicios%5CFrontend%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Lexend%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22lexend%22%7D&modules=C%3A%5CUsers%5CUSUARIO%5COneDrive%5CDocumentos%5CSpringBoot%5CServicios%5CFrontend%5Csrc%5Capp%5Ccomponents%5CThemeProviderWrapper.tsx&modules=C%3A%5CUsers%5CUSUARIO%5COneDrive%5CDocumentos%5CSpringBoot%5CServicios%5CFrontend%5Csrc%5Capp%5Cglobals.css&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/components/ThemeProviderWrapper.tsx */ \"(ssr)/./src/app/components/ThemeProviderWrapper.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDVVNVQVJJTyU1Q09uZURyaXZlJTVDRG9jdW1lbnRvcyU1Q1NwcmluZ0Jvb3QlNUNTZXJ2aWNpb3MlNUNGcm9udGVuZCU1Q25vZGVfbW9kdWxlcyU1Q25leHQlNUNmb250JTVDZ29vZ2xlJTVDdGFyZ2V0LmNzcyUzRiU3QiUyMnBhdGglMjIlM0ElMjJzcmMlNUMlNUNhcHAlNUMlNUNsYXlvdXQudHN4JTIyJTJDJTIyaW1wb3J0JTIyJTNBJTIySW50ZXIlMjIlMkMlMjJhcmd1bWVudHMlMjIlM0ElNUIlN0IlMjJzdWJzZXRzJTIyJTNBJTVCJTIybGF0aW4lMjIlNUQlN0QlNUQlMkMlMjJ2YXJpYWJsZU5hbWUlMjIlM0ElMjJpbnRlciUyMiU3RCZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q1VTVUFSSU8lNUNPbmVEcml2ZSU1Q0RvY3VtZW50b3MlNUNTcHJpbmdCb290JTVDU2VydmljaW9zJTVDRnJvbnRlbmQlNUNub2RlX21vZHVsZXMlNUNuZXh0JTVDZm9udCU1Q2dvb2dsZSU1Q3RhcmdldC5jc3MlM0YlN0IlMjJwYXRoJTIyJTNBJTIyc3JjJTVDJTVDYXBwJTVDJTVDbGF5b3V0LnRzeCUyMiUyQyUyMmltcG9ydCUyMiUzQSUyMkxleGVuZCUyMiUyQyUyMmFyZ3VtZW50cyUyMiUzQSU1QiU3QiUyMnN1YnNldHMlMjIlM0ElNUIlMjJsYXRpbiUyMiU1RCU3RCU1RCUyQyUyMnZhcmlhYmxlTmFtZSUyMiUzQSUyMmxleGVuZCUyMiU3RCZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q1VTVUFSSU8lNUNPbmVEcml2ZSU1Q0RvY3VtZW50b3MlNUNTcHJpbmdCb290JTVDU2VydmljaW9zJTVDRnJvbnRlbmQlNUNzcmMlNUNhcHAlNUNjb21wb25lbnRzJTVDVGhlbWVQcm92aWRlcldyYXBwZXIudHN4Jm1vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDVVNVQVJJTyU1Q09uZURyaXZlJTVDRG9jdW1lbnRvcyU1Q1NwcmluZ0Jvb3QlNUNTZXJ2aWNpb3MlNUNGcm9udGVuZCU1Q3NyYyU1Q2FwcCU1Q2dsb2JhbHMuY3NzJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL2Zyb250ZW5kLz9mYWQ2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcVVNVQVJJT1xcXFxPbmVEcml2ZVxcXFxEb2N1bWVudG9zXFxcXFNwcmluZ0Jvb3RcXFxcU2VydmljaW9zXFxcXEZyb250ZW5kXFxcXHNyY1xcXFxhcHBcXFxcY29tcG9uZW50c1xcXFxUaGVtZVByb3ZpZGVyV3JhcHBlci50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CUSUARIO%5COneDrive%5CDocumentos%5CSpringBoot%5CServicios%5CFrontend%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5CUSUARIO%5COneDrive%5CDocumentos%5CSpringBoot%5CServicios%5CFrontend%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Lexend%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22lexend%22%7D&modules=C%3A%5CUsers%5CUSUARIO%5COneDrive%5CDocumentos%5CSpringBoot%5CServicios%5CFrontend%5Csrc%5Capp%5Ccomponents%5CThemeProviderWrapper.tsx&modules=C%3A%5CUsers%5CUSUARIO%5COneDrive%5CDocumentos%5CSpringBoot%5CServicios%5CFrontend%5Csrc%5Capp%5Cglobals.css&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CUSUARIO%5COneDrive%5CDocumentos%5CSpringBoot%5CServicios%5CFrontend%5Csrc%5Capp%5C(paginas)%5Cdashboard%5Cpage.tsx&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CUSUARIO%5COneDrive%5CDocumentos%5CSpringBoot%5CServicios%5CFrontend%5Csrc%5Capp%5C(paginas)%5Cdashboard%5Cpage.tsx&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(paginas)/dashboard/page.tsx */ \"(ssr)/./src/app/(paginas)/dashboard/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDVVNVQVJJTyU1Q09uZURyaXZlJTVDRG9jdW1lbnRvcyU1Q1NwcmluZ0Jvb3QlNUNTZXJ2aWNpb3MlNUNGcm9udGVuZCU1Q3NyYyU1Q2FwcCU1QyhwYWdpbmFzKSU1Q2Rhc2hib2FyZCU1Q3BhZ2UudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL2Zyb250ZW5kLz8yYzk3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcVVNVQVJJT1xcXFxPbmVEcml2ZVxcXFxEb2N1bWVudG9zXFxcXFNwcmluZ0Jvb3RcXFxcU2VydmljaW9zXFxcXEZyb250ZW5kXFxcXHNyY1xcXFxhcHBcXFxcKHBhZ2luYXMpXFxcXGRhc2hib2FyZFxcXFxwYWdlLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CUSUARIO%5COneDrive%5CDocumentos%5CSpringBoot%5CServicios%5CFrontend%5Csrc%5Capp%5C(paginas)%5Cdashboard%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/(paginas)/dashboard/page.tsx":
/*!**********************************************!*\
  !*** ./src/app/(paginas)/dashboard/page.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Box_Card_CardContent_Grid_IconButton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Card,CardContent,Grid,IconButton,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Card_CardContent_Grid_IconButton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Card,CardContent,Grid,IconButton,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/CardContent/CardContent.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Card_CardContent_Grid_IconButton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Card,CardContent,Grid,IconButton,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Card_CardContent_Grid_IconButton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Card,CardContent,Grid,IconButton,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Card_CardContent_Grid_IconButton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Card,CardContent,Grid,IconButton,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Card_CardContent_Grid_IconButton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Card,CardContent,Grid,IconButton,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _mui_icons_material_TrendingUp__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @mui/icons-material/TrendingUp */ \"(ssr)/./node_modules/@mui/icons-material/TrendingUp.js\");\n/* harmony import */ var _mui_icons_material_CalendarToday__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @mui/icons-material/CalendarToday */ \"(ssr)/./node_modules/@mui/icons-material/CalendarToday.js\");\n/* harmony import */ var _mui_icons_material_LocalGasStation__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @mui/icons-material/LocalGasStation */ \"(ssr)/./node_modules/@mui/icons-material/LocalGasStation.js\");\n/* harmony import */ var _mui_icons_material_AttachMoney__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @mui/icons-material/AttachMoney */ \"(ssr)/./node_modules/@mui/icons-material/AttachMoney.js\");\n/* harmony import */ var _mui_icons_material_Map__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @mui/icons-material/Map */ \"(ssr)/./node_modules/@mui/icons-material/Map.js\");\n/* harmony import */ var _mui_icons_material_Build__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @mui/icons-material/Build */ \"(ssr)/./node_modules/@mui/icons-material/Build.js\");\n// \"use client\";\n// import React, { useEffect, useState } from \"react\";\n// import { Grid, Card, Typography, Box, Skeleton } from \"@mui/material\";\n// import { Timer } from \"@mui/icons-material\";\n// import MetricCard from \"../../components/cards/MetricCard\";\n// import WeatherWidget from \"../../components/weather/WeatherWidget\";\n// import FarmSummary from \"../../components/farm/FarmSummary\";\n// import TaskList from \"../../components/tasks/TaskList\";\n// const Dashboard = () => {\n//   // Estados para el formulario de eventos y visualización\n//   const [selectedDate, setSelectedDate] = useState<string>(\"\");\n//   const [showEventForm, setShowEventForm] = useState(false);\n//   const [showEvents, setShowEvents] = useState(false);\n//   const [events, setEvents] = useState<\n//     Array<{\n//       id: string;\n//       date: string;\n//       title: string;\n//       description: string;\n//       time: string;\n//     }>\n//   >([]);\n//   // Activar el estado de loading\n//   const [loading, setLoading] = useState(true);\n//   // Efecto para simular la carga de datos\n//   useEffect(() => {\n//     const fetchData = async () => {\n//       setLoading(true);\n//       try {\n//         await new Promise((resolve) => setTimeout(resolve, 1500));\n//       } catch (error) {\n//         console.error(\"Error fetching data:\", error);\n//       } finally {\n//         setLoading(false);\n//       }\n//     };\n//     fetchData();\n//   }, []);\n//   // Función para manejar la selección de fecha\n//   const handleDateClick = (date: string) => {\n//     setSelectedDate(date);\n//     setShowEventForm(true);\n//     setShowEvents(false);\n//   };\n//   const formatearFecha = (fecha: Date) => {\n//     const fechaFormateada = fecha.toLocaleDateString(\"es-ES\", {\n//       weekday: \"long\",\n//       day: \"numeric\",\n//       month: \"long\",\n//       year: \"numeric\",\n//     });\n//     // Dividir la cadena en palabras\n//     const palabras = fechaFormateada.split(\" \");\n//     // Capitalizar la primera letra del día y del mes\n//     palabras[0] = palabras[0].charAt(0).toUpperCase() + palabras[0].slice(1); // día de la semana\n//     palabras[3] = palabras[3].charAt(0).toUpperCase() + palabras[3].slice(1); // mes\n//     // Unir las palabras de nuevo\n//     return palabras.join(\" \");\n//   };\n//   // Añadir esta función para determinar la estación actual\n//   const obtenerEstacionActual = () => {\n//     const fecha = new Date();\n//     const mes = fecha.getMonth() + 1; // getMonth() devuelve 0-11\n//     const dia = fecha.getDate();\n//     // Verano: 21 de diciembre - 20 de marzo\n//     if ((mes === 12 && dia >= 21) || mes <= 2 || (mes === 3 && dia <= 20)) {\n//       return \"Verano\";\n//     }\n//     // Otoño: 21 de marzo - 20 de junio\n//     else if ((mes === 3 && dia >= 21) || mes <= 5 || (mes === 6 && dia <= 20)) {\n//       return \"Otoño\";\n//     }\n//     // Invierno: 21 de junio - 20 de septiembre\n//     else if ((mes === 6 && dia >= 21) || mes <= 8 || (mes === 9 && dia <= 20)) {\n//       return \"Invierno\";\n//     }\n//     // Primavera: 21 de septiembre - 20 de diciembre\n//     else {\n//       return \"Primavera\";\n//     }\n//   };\n//   // Función para determinar el ciclo agrícola\n//   const obtenerCicloAgricola = () => {\n//     const estacion = obtenerEstacionActual();\n//     return estacion === \"Otoño\" || estacion === \"Invierno\"\n//       ? \"Otoño-Invierno\"\n//       : \"Primavera-Verano\";\n//   };\n//   // Función para formatear la fecha actual\n//   const formatearFechaActual = () => {\n//     const fecha = new Date();\n//     const fechaFormateada = fecha.toLocaleDateString(\"es-ES\", {\n//       weekday: \"long\",\n//       day: \"numeric\",\n//       month: \"long\",\n//     });\n//     // Dividir la cadena en palabras y capitalizar\n//     const palabras = fechaFormateada.split(\" \");\n//     palabras[0] = palabras[0].charAt(0).toUpperCase() + palabras[0].slice(1); // día de la semana\n//     palabras[3] = palabras[3].charAt(0).toUpperCase() + palabras[3].slice(1); // mes\n//     return palabras.join(\" \");\n//   };\n//   return (\n//     <Box sx={{ padding: \"16px\" }}>\n//       {/* Header Section */}\n//       <Box sx={{ mb: 4 }}>\n//         <Box\n//           sx={{\n//             display: \"flex\",\n//             flexDirection: \"column\",\n//             mb: 3,\n//           }}\n//         >\n//           <Typography\n//             variant=\"h6\"\n//             fontWeight=\"bold\"\n//             sx={{\n//               color: \"#2E7D32\",\n//               fontFamily: \"Lexend, sans-serif\",\n//               fontSize: { xs: \"1.3rem\", sm: \"1.6rem\", md: \"1.9rem\" },\n//               lineHeight: 1.2,\n//               whiteSpace: \"nowrap\",\n//               mb: 1,\n//             }}\n//           >\n//             Bienvenido al Dashboard Agropecuario\n//           </Typography>\n//           <Typography\n//             variant=\"subtitle1\"\n//             sx={{\n//               color: \"#666\",\n//               fontFamily: \"Inter\",\n//               fontSize: { xs: \"0.9rem\", sm: \"1rem\", md: \"1.1rem\" },\n//               lineHeight: 1.3,\n//             }}\n//           >\n//             Gestiona sus Servicios de forma inteligente y eficiente\n//           </Typography>\n//         </Box>\n//       </Box>\n//       <Grid container spacing={3}>\n//         {/* Sección de Cards */}\n//         <Grid item xs={12}>\n//           <Grid container spacing={3}>\n//             <Grid item xs={12} sm={6} md={3}>\n//               <MetricCard\n//                 title=\"Total de Lotes\"\n//                 value={\"\"}\n//                 change=\"+5% desde ayer\"\n//                 icon=\"carduno.png\"\n//                 loading={loading}\n//                 bgColor={\"\"}\n//                 hoverColor={\"\"}\n//               />\n//             </Grid>\n//             <Grid item xs={12} sm={6} md={3}>\n//               <MetricCard\n//                 title=\"Área Total\"\n//                 value={\"\"}\n//                 change=\"+3% desde la semana pasada\"\n//                 icon=\"carddos.png\"\n//                 loading={loading}\n//                 bgColor={\"\"}\n//                 hoverColor={\"\"}\n//               />\n//             </Grid>\n//             <Grid item xs={12} sm={6} md={3}>\n//               <MetricCard\n//                 title=\"Total de Parcelas\"\n//                 value={\"\"}\n//                 change=\"-2% desde el trimestre pasado\"\n//                 icon=\"cardtres.png\"\n//                 loading={loading}\n//                 bgColor={\"\"}\n//                 hoverColor={\"\"}\n//               />\n//             </Grid>\n//             <Grid item xs={12} sm={6} md={3}>\n//               {loading ? (\n//                 <Card\n//                   sx={{\n//                     backgroundColor: \"#f8fafc\",\n//                     borderRadius: \"16px\",\n//                     boxShadow: \"0px 4px 12px rgba(0, 0, 0, 0.1)\",\n//                     padding: \"16px\",\n//                   }}\n//                 >\n//                   <Box\n//                     display=\"flex\"\n//                     justifyContent=\"space-between\"\n//                     alignItems=\"center\"\n//                     mb={2}\n//                   >\n//                     <Skeleton variant=\"text\" width=\"50%\" height={24} />\n//                     <Skeleton variant=\"circular\" width={40} height={40} />\n//                   </Box>\n//                   <Skeleton variant=\"text\" width=\"70%\" height={48} />\n//                   <Skeleton\n//                     variant=\"text\"\n//                     width=\"40%\"\n//                     height={24}\n//                     sx={{ mt: 1 }}\n//                   />\n//                 </Card>\n//               ) : (\n//                 <Card\n//                   sx={{\n//                     padding: (theme) => theme.spacing(2),\n//                     backgroundColor: \"#ffffff\",\n//                     borderRadius: \"8px\",\n//                     border: \"1px solid #E5E7EB\",\n//                     boxShadow: \"2px 2px 0px rgba(31, 142, 235, 0.2)\",\n//                     marginBottom: (theme) => theme.spacing(2),\n//                     transition: \"all 0.2s ease\",\n//                     \"&:hover\": {\n//                       transform: \"translate(-1px, -1px)\",\n//                       boxShadow: \"3px 3px 0px rgba(31, 142, 235, 0.3)\",\n//                     },\n//                   }}\n//                 >\n//                   <Box\n//                     display=\"flex\"\n//                     justifyContent=\"space-between\"\n//                     alignItems=\"center\"\n//                     mb={2}\n//                   >\n//                     <Typography\n//                       variant=\"body1\"\n//                       fontWeight=\"bold\"\n//                       sx={{\n//                         color: \"#000000\",\n//                         fontFamily: \"Lexend, sans-serif\",\n//                       }}\n//                     >\n//                       Temporada Actual\n//                     </Typography>\n//                     <Box\n//                       sx={{\n//                         borderRadius: \"50%\",\n//                         width: \"40px\",\n//                         height: \"40px\",\n//                         display: \"flex\",\n//                         justifyContent: \"center\",\n//                         alignItems: \"center\",\n//                       }}\n//                     >\n//                       <Timer sx={{ color: \"#2196F3\", fontSize: \"24px\" }} />\n//                     </Box>\n//                   </Box>\n//                   <Typography\n//                     variant=\"h4\"\n//                     fontWeight=\"bold\"\n//                     sx={{ color: \"#000000\", fontFamily: \"Inter\" }}\n//                   >\n//                     {obtenerEstacionActual()}\n//                   </Typography>\n//                   <Typography\n//                     variant=\"body2\"\n//                     sx={{\n//                       color: \"#666666\",\n//                       fontWeight: \"600\",\n//                       mt: 1,\n//                     }}\n//                   >\n//                     {obtenerCicloAgricola()}\n//                   </Typography>\n//                   <Typography\n//                     variant=\"body2\"\n//                     sx={{\n//                       color: \"#888888\",\n//                       fontWeight: \"500\",\n//                       mt: 0.5,\n//                       fontSize: \"0.85rem\",\n//                       fontFamily: \"Inter\",\n//                     }}\n//                   >\n//                     {formatearFechaActual()}\n//                   </Typography>\n//                 </Card>\n//               )}\n//             </Grid>\n//           </Grid>\n//         </Grid>\n//         {/* Sección de Farm Summary y Weather Widget */}\n//         <Grid item xs={12}>\n//           <Grid container spacing={3}>\n//             <Grid item xs={12} md={8}>\n//               {loading ? (\n//                 <Box\n//                   sx={{\n//                     bgcolor: \"#F1F8E9\",\n//                     p: 2,\n//                     borderRadius: 2,\n//                     border: \"1px solid #C5E1A5\",\n//                     minHeight: \"200px\", // Altura mínima fija\n//                     maxHeight: \"fit-content\", // Altura máxima adaptable\n//                     display: \"flex\",\n//                     flexDirection: \"column\",\n//                   }}\n//                 >\n//                   {/* Header skeleton */}\n//                   <Box\n//                     sx={{\n//                       display: \"flex\",\n//                       justifyContent: \"space-between\",\n//                       pb: 2,\n//                       borderBottom: \"1px solid #e0e0e0\",\n//                     }}\n//                   >\n//                     <Skeleton variant=\"text\" width=\"40%\" height={32} />\n//                     <Skeleton variant=\"text\" width=\"15%\" height={24} />\n//                   </Box>\n//                   {/* Cards grid skeleton */}\n//                   <Box sx={{ mt: 2, flex: 1 }}>\n//                     <Grid container spacing={2}>\n//                       {[1, 2, 3, 4, 5, 6].map((item) => (\n//                         <Grid item xs={12} sm={6} md={4} key={item}>\n//                           <Box\n//                             sx={{\n//                               p: 2,\n//                               bgcolor: \"#f8fafc\",\n//                               borderRadius: 2,\n//                               height: \"100px\", // Altura fija para cada card\n//                             }}\n//                           >\n//                             <Skeleton variant=\"text\" width=\"80%\" height={24} />\n//                             <Skeleton\n//                               variant=\"text\"\n//                               width=\"60%\"\n//                               height={20}\n//                               sx={{ mt: 1 }}\n//                             />\n//                             <Skeleton\n//                               variant=\"text\"\n//                               width=\"70%\"\n//                               height={20}\n//                               sx={{ mt: 1 }}\n//                             />\n//                           </Box>\n//                         </Grid>\n//                       ))}\n//                     </Grid>\n//                   </Box>\n//                 </Box>\n//               ) : (\n//                 <FarmSummary />\n//               )}\n//             </Grid>\n//             <Grid item xs={12} md={4}>\n//               {loading ? (\n//                 <Box\n//                   sx={{\n//                     bgcolor: \"#F1F8E9\",\n//                     p: 2,\n//                     borderRadius: 2,\n//                     border: \"1px solid #C5E1A5\",\n//                     height: \"100%\",\n//                     maxHeight: \"400px\", // Altura máxima fija\n//                   }}\n//                 >\n//                   <Skeleton variant=\"text\" width=\"60%\" height={24} />\n//                   <Skeleton variant=\"rectangular\" height={200} sx={{ mt: 2 }} />\n//                   <Box sx={{ mt: 2 }}>\n//                     <Skeleton variant=\"text\" width=\"40%\" height={20} />\n//                     <Skeleton variant=\"text\" width=\"60%\" height={20} />\n//                     <Skeleton variant=\"text\" width=\"80%\" height={20} />\n//                   </Box>\n//                 </Box>\n//               ) : (\n//                 <WeatherWidget />\n//               )}\n//             </Grid>\n//           </Grid>\n//         </Grid>\n//         {/* Sección de TaskList */}\n//         <Grid item xs={12}>\n//           <Grid container spacing={3}>\n//             <Grid item xs={12} md={8}>\n//               {loading ? (\n//                 <Box\n//                   sx={{\n//                     bgcolor: \"#F1F8E9\",\n//                     p: 2,\n//                     borderRadius: 2,\n//                     border: \"1px solid #C5E1A5\",\n//                     height: \"100%\",\n//                   }}\n//                 >\n//                   {/* Header skeleton */}\n//                   <Box\n//                     sx={{\n//                       display: \"flex\",\n//                       justifyContent: \"space-between\",\n//                       pb: 2,\n//                       borderBottom: \"1px solid #e0e0e0\",\n//                     }}\n//                   >\n//                     <Skeleton variant=\"text\" width=\"40%\" height={32} />\n//                     <Skeleton variant=\"text\" width=\"15%\" height={24} />\n//                   </Box>\n//                   {/* Tasks skeleton */}\n//                   <Box sx={{ mt: 2 }}>\n//                     {[1, 2, 3, 4, 5].map((item) => (\n//                       <Box\n//                         key={item}\n//                         sx={{\n//                           mb: 2,\n//                           p: 2,\n//                           bgcolor: \"#f8fafc\",\n//                           borderRadius: 2,\n//                         }}\n//                       >\n//                         <Box\n//                           sx={{\n//                             display: \"flex\",\n//                             justifyContent: \"space-between\",\n//                             alignItems: \"flex-start\",\n//                           }}\n//                         >\n//                           <Box sx={{ display: \"flex\", gap: 1, width: \"70%\" }}>\n//                             <Skeleton\n//                               variant=\"circular\"\n//                               width={24}\n//                               height={24}\n//                             />\n//                             <Box sx={{ flex: 1 }}>\n//                               <Skeleton\n//                                 variant=\"text\"\n//                                 width=\"80%\"\n//                                 height={24}\n//                               />\n//                               <Skeleton\n//                                 variant=\"text\"\n//                                 width=\"60%\"\n//                                 height={20}\n//                               />\n//                             </Box>\n//                           </Box>\n//                           <Box\n//                             sx={{\n//                               display: \"flex\",\n//                               flexDirection: \"column\",\n//                               alignItems: \"flex-end\",\n//                               gap: 1,\n//                             }}\n//                           >\n//                             <Skeleton\n//                               variant=\"rectangular\"\n//                               width={80}\n//                               height={24}\n//                               sx={{ borderRadius: 1 }}\n//                             />\n//                             <Skeleton variant=\"text\" width={100} height={20} />\n//                           </Box>\n//                         </Box>\n//                       </Box>\n//                     ))}\n//                   </Box>\n//                 </Box>\n//               ) : (\n//                 <TaskList limit={5} />\n//               )}\n//             </Grid>\n//           </Grid>\n//         </Grid>\n//       </Grid>\n//     </Box>\n//   );\n// };\n// export default Dashboard;\n// Dashboard.jsx\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n/** Simple KPI card */ function KpiCard({ title, value, sub, icon }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Grid_IconButton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        elevation: 2,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Grid_IconButton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Grid_IconButton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                display: \"flex\",\n                alignItems: \"center\",\n                justifyContent: \"space-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Grid_IconButton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Grid_IconButton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                variant: \"subtitle2\",\n                                color: \"text.secondary\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 513,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Grid_IconButton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                variant: \"h5\",\n                                sx: {\n                                    mt: 0.5\n                                },\n                                children: value\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 514,\n                                columnNumber: 13\n                            }, this),\n                            sub && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Grid_IconButton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                variant: \"caption\",\n                                color: \"text.secondary\",\n                                children: sub\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 515,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 512,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Grid_IconButton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Grid_IconButton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            size: \"small\",\n                            children: icon\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 518,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 517,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 511,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 510,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n        lineNumber: 509,\n        columnNumber: 5\n    }, this);\n}\nfunction Dashboard({ data }) {\n    // data puede venir de tu API: ingresos, horas, ha, mantenimientos, etc.\n    // Estos son ejemplos mock.\n    const kpIs = [\n        {\n            id: \"ingresos\",\n            title: \"Ingresos (mes)\",\n            value: `$ ${data?.ingresosMes ?? \"0\"}`,\n            sub: \"vs mes anterior: +8%\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_AttachMoney__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 531,\n                columnNumber: 123\n            }, this)\n        },\n        {\n            id: \"hectareas\",\n            title: \"Hect\\xe1reas atendidas\",\n            value: `${data?.hectareasMes ?? 0} ha`,\n            sub: \"Tipo: siembra/pulverizaci\\xf3n\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Map__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 532,\n                columnNumber: 136\n            }, this)\n        },\n        {\n            id: \"utilizacion\",\n            title: \"Utilizaci\\xf3n maquinaria\",\n            value: `${data?.utilizacion ?? 0}%`,\n            sub: \"Promedio flota\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_TrendingUp__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 533,\n                columnNumber: 125\n            }, this)\n        },\n        {\n            id: \"costohora\",\n            title: \"Costo por hora\",\n            value: `$ ${data?.costoHora ?? 0}`,\n            sub: \"Comb, mano de obra, amort.\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_LocalGasStation__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 534,\n                columnNumber: 126\n            }, this)\n        },\n        {\n            id: \"ontime\",\n            title: \"Trabajos a tiempo\",\n            value: `${data?.onTime ?? 0}%`,\n            sub: \"On-time completion\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_CalendarToday__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 535,\n                columnNumber: 114\n            }, this)\n        },\n        {\n            id: \"mantenimiento\",\n            title: \"Mantenimientos pr\\xf3ximos\",\n            value: `${data?.mantProx ?? 0}`,\n            sub: \"M\\xe1quinas con servicio pendiente\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Build__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 536,\n                columnNumber: 141\n            }, this)\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Grid_IconButton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        p: 2,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Grid_IconButton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                container: true,\n                spacing: 2,\n                children: kpIs.map((kpi)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Grid_IconButton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 4,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(KpiCard, {\n                            ...kpi\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 544,\n                            columnNumber: 13\n                        }, this)\n                    }, kpi.id, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 543,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 541,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Grid_IconButton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                mt: 3,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Grid_IconButton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    container: true,\n                    spacing: 2,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Grid_IconButton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            item: true,\n                            xs: 12,\n                            md: 7,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Grid_IconButton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Grid_IconButton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    style: {\n                                        height: 360\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Grid_IconButton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            variant: \"h6\",\n                                            children: \"Mapa de trabajos (\\xfaltimos 7 d\\xedas)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 554,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Grid_IconButton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            mt: 1,\n                                            children: \"[ComponenteMapboxAqu\\xed]\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 556,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 553,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 552,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 551,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Grid_IconButton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            item: true,\n                            xs: 12,\n                            md: 5,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Grid_IconButton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Grid_IconButton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Grid_IconButton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            variant: \"h6\",\n                                            children: \"Alertas y pr\\xf3ximos mantenimientos\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 563,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Grid_IconButton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            mt: 1,\n                                            children: (data?.alertas ?? []).map((a, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Grid_IconButton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    mb: 1,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Grid_IconButton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            variant: \"body2\",\n                                                            children: a.text\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 568,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Grid_IconButton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            variant: \"caption\",\n                                                            color: \"text.secondary\",\n                                                            children: a.when\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 569,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, i, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 567,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 564,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 562,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 561,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 560,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 550,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 549,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n        lineNumber: 540,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/(paginas)/dashboard/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/components/ThemeProviderWrapper.tsx":
/*!*****************************************************!*\
  !*** ./src/app/components/ThemeProviderWrapper.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProviderWrapper: () => (/* binding */ ThemeProviderWrapper)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_components_ThemeProviderWrapper_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\components\\\\ThemeProviderWrapper.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(ssr)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\components\\\\\\\\ThemeProviderWrapper.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_components_ThemeProviderWrapper_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_components_ThemeProviderWrapper_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @mui/material/styles */ \"(ssr)/./node_modules/@mui/material/styles/createTheme.js\");\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mui/material/styles */ \"(ssr)/./node_modules/@mui/material/styles/ThemeProvider.js\");\n/* harmony import */ var _mui_material_CssBaseline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @mui/material/CssBaseline */ \"(ssr)/./node_modules/@mui/material/CssBaseline/CssBaseline.js\");\n/* __next_internal_client_entry_do_not_use__ ThemeProviderWrapper auto */ \n\n\n\n\n// Crear tema personalizado para Material-UI\nconst theme = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n    palette: {\n        primary: {\n            main: \"#2E7D32\",\n            dark: \"#1B5E20\",\n            light: \"#4CAF50\"\n        },\n        secondary: {\n            main: \"#0FB60B\"\n        },\n        background: {\n            default: \"#F5F5F5\"\n        }\n    },\n    typography: {\n        fontFamily: (next_font_google_target_css_path_src_app_components_ThemeProviderWrapper_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().style).fontFamily\n    }\n});\nfunction ThemeProviderWrapper({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_styles__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        theme: theme,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_CssBaseline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\ThemeProviderWrapper.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, this),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\ThemeProviderWrapper.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/components/ThemeProviderWrapper.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/components/icon/IconoPersonalizado.tsx":
/*!********************************************************!*\
  !*** ./src/app/components/icon/IconoPersonalizado.tsx ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ IconoPersonalizado)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n\n\nfunction IconoPersonalizado({ icono, width, height, style, onClick }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: style,\n        children: [\n            \" \",\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                src: \"/assets/img/\" + icono,\n                alt: \"\",\n                width: width || 24,\n                height: height || 24,\n                onClick: onClick\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\icon\\\\IconoPersonalizado.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\icon\\\\IconoPersonalizado.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2NvbXBvbmVudHMvaWNvbi9JY29ub1BlcnNvbmFsaXphZG8udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQStCO0FBR2hCLFNBQVNDLG1CQUFtQixFQUN6Q0MsS0FBSyxFQUNMQyxLQUFLLEVBQ0xDLE1BQU0sRUFDTkMsS0FBSyxFQUNMQyxPQUFPLEVBUVI7SUFDQyxxQkFDRSw4REFBQ0M7UUFBSUYsT0FBT0E7O1lBQU87MEJBQ2pCLDhEQUFDTCxrREFBS0E7Z0JBQ0pRLEtBQUssaUJBQWlCTjtnQkFDdEJPLEtBQUs7Z0JBQ0xOLE9BQU9BLFNBQVM7Z0JBQ2hCQyxRQUFRQSxVQUFVO2dCQUNsQkUsU0FBU0E7Ozs7Ozs7Ozs7OztBQUlqQiIsInNvdXJjZXMiOlsid2VicGFjazovL2Zyb250ZW5kLy4vc3JjL2FwcC9jb21wb25lbnRzL2ljb24vSWNvbm9QZXJzb25hbGl6YWRvLnRzeD83Mzk5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBJbWFnZSBmcm9tIFwibmV4dC9pbWFnZVwiO1xyXG5pbXBvcnQgeyBDU1NQcm9wZXJ0aWVzIH0gZnJvbSBcInJlYWN0XCI7IC8vIEltcG9ydGEgQ1NTUHJvcGVydGllc1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gSWNvbm9QZXJzb25hbGl6YWRvKHtcclxuICBpY29ubyxcclxuICB3aWR0aCxcclxuICBoZWlnaHQsXHJcbiAgc3R5bGUsIC8vIEFncmVnYSAnc3R5bGUnIGEgbGFzIHByb3BzXHJcbiAgb25DbGljayxcclxuICBcclxufToge1xyXG4gIGljb25vOiBzdHJpbmc7XHJcbiAgd2lkdGg/OiBudW1iZXI7XHJcbiAgaGVpZ2h0PzogbnVtYmVyO1xyXG4gIHN0eWxlPzogQ1NTUHJvcGVydGllczsgLy8gQWdyZWdhICdzdHlsZScgYSBsYXMgcHJvcHNcclxuICBvbkNsaWNrPzogKCkgPT4gdm9pZDtcclxufSkge1xyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2IHN0eWxlPXtzdHlsZX0+IHsvKiBBcGxpY2EgZWwgZXN0aWxvIGFsIGRpdiAqL31cclxuICAgICAgPEltYWdlXHJcbiAgICAgICAgc3JjPXtcIi9hc3NldHMvaW1nL1wiICsgaWNvbm99XHJcbiAgICAgICAgYWx0PXtcIlwifVxyXG4gICAgICAgIHdpZHRoPXt3aWR0aCB8fCAyNH1cclxuICAgICAgICBoZWlnaHQ9e2hlaWdodCB8fCAyNH1cclxuICAgICAgICBvbkNsaWNrPXtvbkNsaWNrfVxyXG4gICAgICAvPlxyXG4gICAgPC9kaXY+XHJcbiAgKTtcclxufSJdLCJuYW1lcyI6WyJJbWFnZSIsIkljb25vUGVyc29uYWxpemFkbyIsImljb25vIiwid2lkdGgiLCJoZWlnaHQiLCJzdHlsZSIsIm9uQ2xpY2siLCJkaXYiLCJzcmMiLCJhbHQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/app/components/icon/IconoPersonalizado.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/components/menu/CustomTooltip.js":
/*!**************************************************!*\
  !*** ./src/app/components/menu/CustomTooltip.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Tooltip_styled_tooltipClasses_mui_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Tooltip,styled,tooltipClasses!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/styles/styled.js\");\n/* harmony import */ var _barrel_optimize_names_Tooltip_styled_tooltipClasses_mui_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Tooltip,styled,tooltipClasses!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Tooltip/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Tooltip_styled_tooltipClasses_mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Tooltip,styled,tooltipClasses!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Tooltip/tooltipClasses.js\");\n\n\n\n// ✨ Tooltip personalizado\nconst CustomTooltip = (0,_barrel_optimize_names_Tooltip_styled_tooltipClasses_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(({ className, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Tooltip_styled_tooltipClasses_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        ...props,\n        classes: {\n            popper: className\n        },\n        arrow: true\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\menu\\\\CustomTooltip.js\",\n        lineNumber: 6,\n        columnNumber: 3\n    }, undefined))(({ theme })=>({\n        [`& .${_barrel_optimize_names_Tooltip_styled_tooltipClasses_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"].tooltip}`]: {\n            backgroundColor: \"#4CAF50\",\n            color: \"#FFF\",\n            fontSize: \"14px\",\n            borderRadius: \"8px\",\n            boxShadow: \"0px 4px 10px rgba(0, 0, 0, 0.4)\",\n            borderRadius: \"12px\",\n            padding: \"10px 16px\",\n            fontFamily: \"Arial, sans-serif\",\n            maxWidth: \"200px\",\n            transition: \"all 0.3s ease-in-out\"\n        },\n        [`& .${_barrel_optimize_names_Tooltip_styled_tooltipClasses_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"].arrow}`]: {\n            color: \"#4CAF50\"\n        }\n    }));\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CustomTooltip);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/components/menu/CustomTooltip.js\n");

/***/ }),

/***/ "(ssr)/./src/app/components/menu/ElementoLista.tsx":
/*!***************************************************!*\
  !*** ./src/app/components/menu/ElementoLista.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ElementoLista)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ListItem_ListItemIcon_ListItemText_mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ListItem,ListItemIcon,ListItemText!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/ListItemText/ListItemText.js\");\n/* harmony import */ var _barrel_optimize_names_ListItem_ListItemIcon_ListItemText_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ListItem,ListItemIcon,ListItemText!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/ListItem/ListItem.js\");\n/* harmony import */ var _barrel_optimize_names_ListItem_ListItemIcon_ListItemText_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ListItem,ListItemIcon,ListItemText!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/ListItemIcon/ListItemIcon.js\");\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mui/material/styles */ \"(ssr)/./node_modules/@mui/material/styles/styled.js\");\n/* harmony import */ var _components_menu_CustomTooltip__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../components/menu/CustomTooltip */ \"(ssr)/./src/app/components/menu/CustomTooltip.js\");\n\n\n\n\n\nconst CustomListItemText = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(_barrel_optimize_names_ListItem_ListItemIcon_ListItemText_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(({ theme })=>({\n        \"& .MuiListItemText-primary\": {\n            fontFamily: \"Inter, sans-serif\",\n            fontSize: \"1rem\",\n            color: theme.palette.text.primary\n        }\n    }));\nfunction ElementoLista({ icon, open, text, onClick, selected, tooltipText, disableSelectedColor = false, customStyle }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_menu_CustomTooltip__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        title: tooltipText,\n        placement: \"right\",\n        arrow: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ListItem_ListItemIcon_ListItemText_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            button: true,\n            selected: selected,\n            onClick: onClick,\n            sx: {\n                padding: \"12px 16px\",\n                minHeight: \"56px\",\n                backgroundColor: selected ? disableSelectedColor ? \"transparent\" : \"inherit\" : \"inherit\",\n                \"&.Mui-selected\": {\n                    backgroundColor: \"#F2F2F2\",\n                    \"& .MuiListItemText-primary\": {\n                        color: disableSelectedColor ? \"inherit\" : \"#2E7D32\",\n                        fontFamily: \"Inter, sans-serif\",\n                        transition: \"color 0.3s ease\"\n                    },\n                    transition: \"background-color 0.3s ease\"\n                },\n                cursor: \"pointer\",\n                \"&:hover\": {\n                    backgroundColor: \"#F0F0F0\"\n                },\n                fontFamily: \"Inter, sans-serif\",\n                ...customStyle\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ListItem_ListItemIcon_ListItemText_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    sx: {\n                        fontFamily: \"Inter, sans-serif\",\n                        fontSize: \"24px\"\n                    },\n                    children: icon\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\menu\\\\ElementoLista.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, this),\n                open && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CustomListItemText, {\n                    primary: text\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\menu\\\\ElementoLista.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 18\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\menu\\\\ElementoLista.tsx\",\n            lineNumber: 37,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\menu\\\\ElementoLista.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2NvbXBvbmVudHMvbWVudS9FbGVtZW50b0xpc3RhLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUF5QztBQUM0QjtBQUN2QjtBQUNrQjtBQWFoRSxNQUFNTSxxQkFBcUJGLGdFQUFNQSxDQUFDRCw4R0FBWUEsRUFBRSxDQUFDLEVBQUVJLEtBQUssRUFBRSxHQUFNO1FBQzlELDhCQUE4QjtZQUM1QkMsWUFBWTtZQUNaQyxVQUFVO1lBQ1ZDLE9BQU9ILE1BQU1JLE9BQU8sQ0FBQ0MsSUFBSSxDQUFDQyxPQUFPO1FBQ25DO0lBQ0Y7QUFFZSxTQUFTQyxjQUFjLEVBQ3BDQyxJQUFJLEVBQ0pDLElBQUksRUFDSkosSUFBSSxFQUNKSyxPQUFPLEVBQ1BDLFFBQVEsRUFDUkMsV0FBVyxFQUNYQyx1QkFBdUIsS0FBSyxFQUM1QkMsV0FBVyxFQUNRO0lBQ25CLHFCQUNFLDhEQUFDaEIsc0VBQWFBO1FBQUNpQixPQUFPSDtRQUFhSSxXQUFVO1FBQVFDLEtBQUs7a0JBQ3hELDRFQUFDdkIsOEdBQVFBO1lBQ1B3QixNQUFNO1lBQ05QLFVBQVVBO1lBQ1ZELFNBQVNBO1lBQ1RTLElBQUk7Z0JBQ0ZDLFNBQVM7Z0JBQ1RDLFdBQVc7Z0JBQ1hDLGlCQUFpQlgsV0FDYkUsdUJBQ0UsZ0JBQ0EsWUFDRjtnQkFDSixrQkFBa0I7b0JBQ2hCUyxpQkFBaUI7b0JBQ2pCLDhCQUE4Qjt3QkFDNUJuQixPQUFPVSx1QkFBdUIsWUFBWTt3QkFDMUNaLFlBQVk7d0JBQ1pzQixZQUFZO29CQUNkO29CQUNBQSxZQUFZO2dCQUNkO2dCQUNBQyxRQUFRO2dCQUNSLFdBQVc7b0JBQUVGLGlCQUFpQjtnQkFBVTtnQkFDeENyQixZQUFZO2dCQUNaLEdBQUdhLFdBQVc7WUFDaEI7OzhCQUVBLDhEQUFDbkIsOEdBQVlBO29CQUNYd0IsSUFBSTt3QkFBRWxCLFlBQVk7d0JBQXFCQyxVQUFVO29CQUFPOzhCQUV2RE07Ozs7OztnQkFFRkMsc0JBQVEsOERBQUNWO29CQUFtQk8sU0FBU0Q7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBSTlDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZnJvbnRlbmQvLi9zcmMvYXBwL2NvbXBvbmVudHMvbWVudS9FbGVtZW50b0xpc3RhLnRzeD8yMDZmIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCwgeyB1c2VFZmZlY3QgfSBmcm9tIFwicmVhY3RcIjtcclxuaW1wb3J0IHsgTGlzdEl0ZW0sIExpc3RJdGVtSWNvbiwgTGlzdEl0ZW1UZXh0IH0gZnJvbSBcIkBtdWkvbWF0ZXJpYWxcIjtcclxuaW1wb3J0IHsgc3R5bGVkIH0gZnJvbSBcIkBtdWkvbWF0ZXJpYWwvc3R5bGVzXCI7XHJcbmltcG9ydCBDdXN0b21Ub29sdGlwIGZyb20gXCIuLi8uLi9jb21wb25lbnRzL21lbnUvQ3VzdG9tVG9vbHRpcFwiO1xyXG5cclxuaW50ZXJmYWNlIEVsZW1lbnRvTGlzdGFQcm9wcyB7XHJcbiAgaWNvbjogUmVhY3QuUmVhY3ROb2RlO1xyXG4gIG9wZW46IGJvb2xlYW47XHJcbiAgdGV4dDogc3RyaW5nO1xyXG4gIG9uQ2xpY2s6ICgpID0+IHZvaWQ7XHJcbiAgc2VsZWN0ZWQ6IGJvb2xlYW47XHJcbiAgdG9vbHRpcFRleHQ6IHN0cmluZztcclxuICBkaXNhYmxlU2VsZWN0ZWRDb2xvcj86IGJvb2xlYW47XHJcbiAgY3VzdG9tU3R5bGU/OiBSZWFjdC5DU1NQcm9wZXJ0aWVzO1xyXG59XHJcblxyXG5jb25zdCBDdXN0b21MaXN0SXRlbVRleHQgPSBzdHlsZWQoTGlzdEl0ZW1UZXh0KSgoeyB0aGVtZSB9KSA9PiAoe1xyXG4gIFwiJiAuTXVpTGlzdEl0ZW1UZXh0LXByaW1hcnlcIjoge1xyXG4gICAgZm9udEZhbWlseTogXCJJbnRlciwgc2Fucy1zZXJpZlwiLCAvLyBDYW1iaWFkbyBhIEludGVyXHJcbiAgICBmb250U2l6ZTogXCIxcmVtXCIsXHJcbiAgICBjb2xvcjogdGhlbWUucGFsZXR0ZS50ZXh0LnByaW1hcnksXHJcbiAgfSxcclxufSkpO1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gRWxlbWVudG9MaXN0YSh7XHJcbiAgaWNvbixcclxuICBvcGVuLFxyXG4gIHRleHQsXHJcbiAgb25DbGljayxcclxuICBzZWxlY3RlZCxcclxuICB0b29sdGlwVGV4dCxcclxuICBkaXNhYmxlU2VsZWN0ZWRDb2xvciA9IGZhbHNlLFxyXG4gIGN1c3RvbVN0eWxlLFxyXG59OiBFbGVtZW50b0xpc3RhUHJvcHMpIHtcclxuICByZXR1cm4gKFxyXG4gICAgPEN1c3RvbVRvb2x0aXAgdGl0bGU9e3Rvb2x0aXBUZXh0fSBwbGFjZW1lbnQ9XCJyaWdodFwiIGFycm93PlxyXG4gICAgICA8TGlzdEl0ZW1cclxuICAgICAgICBidXR0b25cclxuICAgICAgICBzZWxlY3RlZD17c2VsZWN0ZWR9XHJcbiAgICAgICAgb25DbGljaz17b25DbGlja31cclxuICAgICAgICBzeD17e1xyXG4gICAgICAgICAgcGFkZGluZzogXCIxMnB4IDE2cHhcIixcclxuICAgICAgICAgIG1pbkhlaWdodDogXCI1NnB4XCIsXHJcbiAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6IHNlbGVjdGVkXHJcbiAgICAgICAgICAgID8gZGlzYWJsZVNlbGVjdGVkQ29sb3JcclxuICAgICAgICAgICAgICA/IFwidHJhbnNwYXJlbnRcIlxyXG4gICAgICAgICAgICAgIDogXCJpbmhlcml0XCJcclxuICAgICAgICAgICAgOiBcImluaGVyaXRcIixcclxuICAgICAgICAgIFwiJi5NdWktc2VsZWN0ZWRcIjoge1xyXG4gICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6IFwiI0YyRjJGMlwiLFxyXG4gICAgICAgICAgICBcIiYgLk11aUxpc3RJdGVtVGV4dC1wcmltYXJ5XCI6IHtcclxuICAgICAgICAgICAgICBjb2xvcjogZGlzYWJsZVNlbGVjdGVkQ29sb3IgPyBcImluaGVyaXRcIiA6IFwiIzJFN0QzMlwiLFxyXG4gICAgICAgICAgICAgIGZvbnRGYW1pbHk6IFwiSW50ZXIsIHNhbnMtc2VyaWZcIiwgLy8gQ2FtYmlhZG8gYSBJbnRlclxyXG4gICAgICAgICAgICAgIHRyYW5zaXRpb246IFwiY29sb3IgMC4zcyBlYXNlXCIsXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIHRyYW5zaXRpb246IFwiYmFja2dyb3VuZC1jb2xvciAwLjNzIGVhc2VcIixcclxuICAgICAgICAgIH0sXHJcbiAgICAgICAgICBjdXJzb3I6IFwicG9pbnRlclwiLFxyXG4gICAgICAgICAgXCImOmhvdmVyXCI6IHsgYmFja2dyb3VuZENvbG9yOiBcIiNGMEYwRjBcIiB9LFxyXG4gICAgICAgICAgZm9udEZhbWlseTogXCJJbnRlciwgc2Fucy1zZXJpZlwiLCAvLyBDYW1iaWFkbyBhIEludGVyXHJcbiAgICAgICAgICAuLi5jdXN0b21TdHlsZSxcclxuICAgICAgICB9fVxyXG4gICAgICA+XHJcbiAgICAgICAgPExpc3RJdGVtSWNvblxyXG4gICAgICAgICAgc3g9e3sgZm9udEZhbWlseTogXCJJbnRlciwgc2Fucy1zZXJpZlwiLCBmb250U2l6ZTogXCIyNHB4XCIgfX0gLy8gQ2FtYmlhZG8gYSBJbnRlclxyXG4gICAgICAgID5cclxuICAgICAgICAgIHtpY29ufVxyXG4gICAgICAgIDwvTGlzdEl0ZW1JY29uPlxyXG4gICAgICAgIHtvcGVuICYmIDxDdXN0b21MaXN0SXRlbVRleHQgcHJpbWFyeT17dGV4dH0gLz59XHJcbiAgICAgIDwvTGlzdEl0ZW0+XHJcbiAgICA8L0N1c3RvbVRvb2x0aXA+XHJcbiAgKTtcclxufVxyXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJMaXN0SXRlbSIsIkxpc3RJdGVtSWNvbiIsIkxpc3RJdGVtVGV4dCIsInN0eWxlZCIsIkN1c3RvbVRvb2x0aXAiLCJDdXN0b21MaXN0SXRlbVRleHQiLCJ0aGVtZSIsImZvbnRGYW1pbHkiLCJmb250U2l6ZSIsImNvbG9yIiwicGFsZXR0ZSIsInRleHQiLCJwcmltYXJ5IiwiRWxlbWVudG9MaXN0YSIsImljb24iLCJvcGVuIiwib25DbGljayIsInNlbGVjdGVkIiwidG9vbHRpcFRleHQiLCJkaXNhYmxlU2VsZWN0ZWRDb2xvciIsImN1c3RvbVN0eWxlIiwidGl0bGUiLCJwbGFjZW1lbnQiLCJhcnJvdyIsImJ1dHRvbiIsInN4IiwicGFkZGluZyIsIm1pbkhlaWdodCIsImJhY2tncm91bmRDb2xvciIsInRyYW5zaXRpb24iLCJjdXJzb3IiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/app/components/menu/ElementoLista.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/components/menu/MenuPrincipal.tsx":
/*!***************************************************!*\
  !*** ./src/app/components/menu/MenuPrincipal.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MenuPrincipal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @mui/material/styles */ \"(ssr)/./node_modules/@mui/material/styles/styled.js\");\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @mui/material/styles */ \"(ssr)/./node_modules/@mui/material/styles/useTheme.js\");\n/* harmony import */ var _mui_material_Box__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @mui/material/Box */ \"(ssr)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _mui_material_Drawer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @mui/material/Drawer */ \"(ssr)/./node_modules/@mui/material/Drawer/Drawer.js\");\n/* harmony import */ var _mui_material_AppBar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @mui/material/AppBar */ \"(ssr)/./node_modules/@mui/material/AppBar/AppBar.js\");\n/* harmony import */ var _barrel_optimize_names_Stack_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Stack,Toolbar,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Toolbar/Toolbar.js\");\n/* harmony import */ var _barrel_optimize_names_Stack_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Stack,Toolbar,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Stack_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Stack,Toolbar,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var _mui_material_List__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @mui/material/List */ \"(ssr)/./node_modules/@mui/material/List/List.js\");\n/* harmony import */ var _barrel_optimize_names_IconButton_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=IconButton!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _ElementoLista__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ElementoLista */ \"(ssr)/./src/app/components/menu/ElementoLista.tsx\");\n/* harmony import */ var _icon_IconoPersonalizado__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../icon/IconoPersonalizado */ \"(ssr)/./src/app/components/icon/IconoPersonalizado.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _mui_icons_material_Menu__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @mui/icons-material/Menu */ \"(ssr)/./node_modules/@mui/icons-material/Menu.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _mui_material_Modal__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @mui/material/Modal */ \"(ssr)/./node_modules/@mui/material/Modal/Modal.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\n\n\n\n // Modal\nconst drawerWidth = 240;\nconst openedMixin = (theme)=>({\n        width: drawerWidth,\n        transition: theme.transitions.create(\"width\", {\n            easing: theme.transitions.easing.sharp,\n            duration: theme.transitions.duration.enteringScreen\n        }),\n        overflowX: \"hidden\"\n    });\nconst closedMixin = (theme)=>({\n        transition: theme.transitions.create(\"width\", {\n            easing: theme.transitions.easing.sharp,\n            duration: theme.transitions.duration.leavingScreen\n        }),\n        overflowX: \"hidden\",\n        width: `calc(${theme.spacing(6)} + 1px)`,\n        [theme.breakpoints.up(\"sm\")]: {\n            width: `calc(${theme.spacing(7)} + 1px)`\n        }\n    });\nconst DrawerHeader = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(\"div\")(({ theme })=>({\n        display: \"flex\",\n        alignItems: \"center\",\n        justifyContent: \"flex-end\",\n        padding: theme.spacing(0, 1),\n        ...theme.mixins.toolbar\n    }));\nconst AppBar = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_mui_material_AppBar__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n    shouldForwardProp: (prop)=>prop !== \"open\"\n})(({ theme, open })=>({\n        zIndex: theme.zIndex.drawer + 1,\n        transition: theme.transitions.create([\n            \"width\",\n            \"margin\"\n        ], {\n            easing: theme.transitions.easing.sharp,\n            duration: theme.transitions.duration.enteringScreen\n        }),\n        ...open && {\n            marginLeft: drawerWidth,\n            width: `calc(100% - ${drawerWidth}px)`,\n            transition: theme.transitions.create([\n                \"width\",\n                \"margin\"\n            ], {\n                easing: theme.transitions.easing.sharp,\n                duration: theme.transitions.duration.enteringScreen\n            })\n        },\n        ...!open && {\n            marginLeft: `calc(${theme.spacing(7)} + 1px)`,\n            width: `calc(100% - ${theme.spacing(7)} - 1px)`,\n            transition: theme.transitions.create([\n                \"width\",\n                \"margin\"\n            ], {\n                easing: theme.transitions.easing.sharp,\n                duration: theme.transitions.duration.leavingScreen\n            })\n        }\n    }));\nconst Drawer = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_mui_material_Drawer__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n    shouldForwardProp: (prop)=>prop !== \"open\"\n})(({ theme, open })=>({\n        width: drawerWidth,\n        flexShrink: 0,\n        whiteSpace: \"nowrap\",\n        boxSizing: \"border-box\",\n        ...open && {\n            ...openedMixin(theme),\n            \"& .MuiDrawer-paper\": openedMixin(theme)\n        },\n        ...!open && {\n            ...closedMixin(theme),\n            \"& .MuiDrawer-paper\": closedMixin(theme)\n        }\n    }));\nconst StyledToolbar = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_barrel_optimize_names_Stack_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"])({\n    backgroundColor: \"#2E7D32\",\n    color: \"#FFF\",\n    height: \"80px\",\n    padding: \"0 16px\",\n    boxShadow: \"0px 1px 10px 1px rgba(0,0,0,0.1)\",\n    fontFamily: \"var(--font-sans)\"\n});\nconst StyledIconButton = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_barrel_optimize_names_IconButton_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"])({\n    color: \"#FFF\"\n});\nconst CustomTypography = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_barrel_optimize_names_Stack_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"])({\n    fontFamily: \"var(--font-serif)\"\n});\nconst MenuIcon = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_mui_icons_material_Menu__WEBPACK_IMPORTED_MODULE_11__[\"default\"])({\n    fontSize: \"32px\"\n});\nfunction MenuPrincipal({ children }) {\n    const theme = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_12__[\"default\"])();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const [open, setOpen] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(false);\n    const [selectedIndex, setSelectedIndex] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(null);\n    const [openCalendarModal, setOpenCalendarModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // Estado del modal\n    const handleListItemClick = (index, path)=>{\n        setSelectedIndex(index);\n        router.push(path);\n    };\n    const items = [\n        {\n            text: \"Dashboard\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_icon_IconoPersonalizado__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                icono: \"panel.png\",\n                width: 32,\n                height: 32\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\menu\\\\MenuPrincipal.tsx\",\n                lineNumber: 142,\n                columnNumber: 13\n            }, this),\n            path: \"/dashboard\",\n            tooltip: \"Dashboard\"\n        },\n        {\n            text: \"Agricultor/Ganadero\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_icon_IconoPersonalizado__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                icono: \"granjero.png\",\n                width: 32,\n                height: 32\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\menu\\\\MenuPrincipal.tsx\",\n                lineNumber: 149,\n                columnNumber: 9\n            }, this),\n            path: \"/agricultor\",\n            tooltip: \"Agricultor/Ganadero\"\n        },\n        {\n            text: \"Establecimiento\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_icon_IconoPersonalizado__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                icono: \"establecimiento.png\",\n                width: 32,\n                height: 32\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\menu\\\\MenuPrincipal.tsx\",\n                lineNumber: 157,\n                columnNumber: 9\n            }, this),\n            path: \"/establecimiento\",\n            tooltip: \"Establecimiento\"\n        },\n        {\n            text: \"Servicios\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_icon_IconoPersonalizado__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                icono: \"servicios.png\",\n                width: 32,\n                height: 32\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\menu\\\\MenuPrincipal.tsx\",\n                lineNumber: 169,\n                columnNumber: 9\n            }, this),\n            path: \"/servicio\",\n            tooltip: \"Servicio\"\n        },\n        {\n            text: \"Insumos\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_icon_IconoPersonalizado__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                icono: \"productos.png\",\n                width: 32,\n                height: 32\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\menu\\\\MenuPrincipal.tsx\",\n                lineNumber: 177,\n                columnNumber: 9\n            }, this),\n            path: \"/insumo\",\n            tooltip: \"Insumo\"\n        },\n        {\n            text: \"Tareas\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_icon_IconoPersonalizado__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                icono: \"tareas.png\",\n                width: 32,\n                height: 32\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\menu\\\\MenuPrincipal.tsx\",\n                lineNumber: 184,\n                columnNumber: 13\n            }, this),\n            path: \"/tareas\",\n            tooltip: \"Tareas\"\n        },\n        {\n            text: \"Documentos\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_icon_IconoPersonalizado__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                icono: \"documentos.png\",\n                width: 32,\n                height: 32\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\menu\\\\MenuPrincipal.tsx\",\n                lineNumber: 191,\n                columnNumber: 9\n            }, this),\n            path: \"/documentos\",\n            tooltip: \"Documentos\"\n        },\n        {\n            text: \"Estad\\xedsticas\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_icon_IconoPersonalizado__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                icono: \"graficos.png\",\n                width: 32,\n                height: 32\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\menu\\\\MenuPrincipal.tsx\",\n                lineNumber: 199,\n                columnNumber: 9\n            }, this),\n            path: \"/graficos\",\n            tooltip: \"Graficos\"\n        }\n    ];\n    const iconVariant = {\n        hover: {\n            transition: {\n                duration: 0.5\n            }\n        },\n        initial: {\n            scale: 0\n        },\n        animate: {\n            scale: 1,\n            transition: {\n                duration: 0.8\n            }\n        }\n    };\n    const textVariant = {\n        initial: {\n            y: 20,\n            opacity: 0\n        },\n        animate: {\n            y: 0,\n            opacity: 1,\n            transition: {\n                duration: 1\n            }\n        }\n    };\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Simula un tiempo de carga de datos\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timer = setTimeout(()=>{\n            setIsLoading(false);\n        }, 2000); // 2 segundos\n        return ()=>clearTimeout(timer);\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n        sx: {\n            display: \"flex\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AppBar, {\n                position: \"fixed\",\n                open: open,\n                sx: {\n                    backgroundColor: \"#0FB60B\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StyledToolbar, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StyledIconButton, {\n                            color: \"inherit\",\n                            \"aria-label\": open ? \"close drawer\" : \"open drawer\",\n                            onClick: ()=>setOpen(!open),\n                            edge: \"start\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MenuIcon, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\menu\\\\MenuPrincipal.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\menu\\\\MenuPrincipal.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            sx: {\n                                flexGrow: 1\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\menu\\\\MenuPrincipal.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\menu\\\\MenuPrincipal.tsx\",\n                    lineNumber: 230,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\menu\\\\MenuPrincipal.tsx\",\n                lineNumber: 229,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Drawer, {\n                variant: \"permanent\",\n                open: open,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DrawerHeader, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\menu\\\\MenuPrincipal.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_List__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Stack_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            direction: \"row\",\n                            alignItems: \"center\",\n                            spacing: 1,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_16__.motion.img, {\n                                    src: \"/assets/img/tractores.png\",\n                                    alt: \"Tractores\",\n                                    width: 32,\n                                    height: 32,\n                                    style: {\n                                        marginTop: \"-85px\",\n                                        marginLeft: \"15px\"\n                                    },\n                                    variants: iconVariant,\n                                    initial: \"initial\",\n                                    animate: \"animate\",\n                                    whileHover: \"hover\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\menu\\\\MenuPrincipal.tsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_16__.motion.h3, {\n                                    style: {\n                                        marginTop: \"-85px\",\n                                        paddingLeft: \"2px\",\n                                        color: \"#EC9107\",\n                                        letterSpacing: \"1px\"\n                                    },\n                                    variants: textVariant,\n                                    initial: \"initial\",\n                                    animate: \"animate\",\n                                    children: \"AgroContratistas\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\menu\\\\MenuPrincipal.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\menu\\\\MenuPrincipal.tsx\",\n                            lineNumber: 247,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\menu\\\\MenuPrincipal.tsx\",\n                        lineNumber: 246,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_List__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        sx: {\n                            marginTop: 2\n                        },\n                        children: items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ElementoLista__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                icon: item.icon,\n                                open: open,\n                                text: item.text,\n                                onClick: ()=>handleListItemClick(index + 1, item.path),\n                                selected: selectedIndex === index + 1,\n                                tooltipText: item.tooltip\n                            }, item.text, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\menu\\\\MenuPrincipal.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\menu\\\\MenuPrincipal.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            marginTop: \"auto\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_List__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ElementoLista__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_icon_IconoPersonalizado__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    icono: \"salir.png\",\n                                    width: 32,\n                                    height: 32\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\menu\\\\MenuPrincipal.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 17\n                                }, void 0),\n                                open: open,\n                                text: \"Cerrar Sesi\\xf3n\",\n                                onClick: ()=>{\n                                    router.push(\"/auth/container\");\n                                },\n                                selected: false,\n                                tooltipText: \"\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\menu\\\\MenuPrincipal.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\menu\\\\MenuPrincipal.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\menu\\\\MenuPrincipal.tsx\",\n                        lineNumber: 287,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\menu\\\\MenuPrincipal.tsx\",\n                lineNumber: 244,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                component: \"main\",\n                sx: {\n                    flexGrow: 1,\n                    p: 3,\n                    marginLeft: open ? `${drawerWidth}px` : `calc(${theme.spacing(7)} + 1px)`,\n                    transition: theme.transitions.create(\"margin\", {\n                        easing: theme.transitions.easing.sharp,\n                        duration: theme.transitions.duration.enteringScreen\n                    }),\n                    fontFamily: \"var(--font-sans)\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DrawerHeader, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\menu\\\\MenuPrincipal.tsx\",\n                        lineNumber: 324,\n                        columnNumber: 9\n                    }, this),\n                    children\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\menu\\\\MenuPrincipal.tsx\",\n                lineNumber: 309,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Modal__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                open: openCalendarModal,\n                onClose: ()=>setOpenCalendarModal(false),\n                \"aria-labelledby\": \"calendar-modal-title\",\n                \"aria-describedby\": \"calendar-modal-description\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    sx: {\n                        position: \"absolute\",\n                        top: \"50%\",\n                        left: \"50%\",\n                        transform: \"translate(-50%, -50%)\",\n                        bgcolor: \"background.paper\",\n                        border: \"2px solid #000\",\n                        boxShadow: 24,\n                        p: 4\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\menu\\\\MenuPrincipal.tsx\",\n                    lineNumber: 335,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\menu\\\\MenuPrincipal.tsx\",\n                lineNumber: 329,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\menu\\\\MenuPrincipal.tsx\",\n        lineNumber: 228,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/components/menu/MenuPrincipal.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"e3506ed8b95e\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZnJvbnRlbmQvLi9zcmMvYXBwL2dsb2JhbHMuY3NzP2E2YjMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJlMzUwNmVkOGI5NWVcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/(paginas)/dashboard/page.tsx":
/*!**********************************************!*\
  !*** ./src/app/(paginas)/dashboard/page.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   $$typeof: () => (/* binding */ $$typeof),\n/* harmony export */   __esModule: () => (/* binding */ __esModule),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js\");\n\nconst proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\\Users\\<USER>\\OneDrive\\Documentos\\SpringBoot\\Servicios\\Frontend\\src\\app\\(paginas)\\dashboard\\page.tsx`)\n\n// Accessing the __esModule property and exporting $$typeof are required here.\n// The __esModule getter forces the proxy target to create the default export\n// and the $$typeof value is for rendering logic to determine if the module\n// is a client boundary.\nconst { __esModule, $$typeof } = proxy;\nconst __default__ = proxy.default;\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/(paginas)/dashboard/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/(paginas)/layout.tsx":
/*!**************************************!*\
  !*** ./src/app/(paginas)/layout.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_paginas_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\(paginas)\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\(paginas)\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_paginas_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_paginas_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_menu_MenuPrincipal__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/menu/MenuPrincipal */ \"(rsc)/./src/app/components/menu/MenuPrincipal.tsx\");\n\n\n\n\nconst metadata = {};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"es\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_paginas_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_menu_MenuPrincipal__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\layout.tsx\",\n                lineNumber: 18,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\layout.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\layout.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwLyhwYWdpbmFzKS9sYXlvdXQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFLTUE7QUFIeUI7QUFDOEI7QUFJdEQsTUFBTUcsV0FBcUIsQ0FBQyxFQUFFO0FBRXRCLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHVDtJQUNDLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUFLQyxXQUFXVCx1S0FBZTtzQkFDOUIsNEVBQUNFLHNFQUFhQTswQkFBRUc7Ozs7Ozs7Ozs7Ozs7Ozs7QUFJeEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mcm9udGVuZC8uL3NyYy9hcHAvKHBhZ2luYXMpL2xheW91dC50c3g/YzNhNiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSBcIm5leHRcIjtcbmltcG9ydCB7IEludGVyIH0gZnJvbSBcIm5leHQvZm9udC9nb29nbGVcIjtcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IE1lbnVQcmluY2lwYWwgZnJvbSBcIi4uL2NvbXBvbmVudHMvbWVudS9NZW51UHJpbmNpcGFsXCI7XG5cbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbXCJsYXRpblwiXSB9KTtcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHt9O1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcbiAgY2hpbGRyZW4sXG59OiB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG59KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVzXCI+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9e2ludGVyLmNsYXNzTmFtZX0+XG4gICAgICAgIDxNZW51UHJpbmNpcGFsPntjaGlsZHJlbn08L01lbnVQcmluY2lwYWw+XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApO1xufVxuIl0sIm5hbWVzIjpbImludGVyIiwiUmVhY3QiLCJNZW51UHJpbmNpcGFsIiwibWV0YWRhdGEiLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiLCJjbGFzc05hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/(paginas)/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/components/ThemeProviderWrapper.tsx":
/*!*****************************************************!*\
  !*** ./src/app/components/ThemeProviderWrapper.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProviderWrapper: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Documentos\SpringBoot\Servicios\Frontend\src\app\components\ThemeProviderWrapper.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Documentos\SpringBoot\Servicios\Frontend\src\app\components\ThemeProviderWrapper.tsx#ThemeProviderWrapper`);


/***/ }),

/***/ "(rsc)/./src/app/components/menu/MenuPrincipal.tsx":
/*!***************************************************!*\
  !*** ./src/app/components/menu/MenuPrincipal.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Documentos\SpringBoot\Servicios\Frontend\src\app\components\menu\MenuPrincipal.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_ThemeProviderWrapper__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/ThemeProviderWrapper */ \"(rsc)/./src/app/components/ThemeProviderWrapper.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"AgroServicios\",\n    description: \"Gesti\\xf3n de Servicios Agropecuarios\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"es\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ThemeProviderWrapper__WEBPACK_IMPORTED_MODULE_3__.ThemeProviderWrapper, {\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 23,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFNTUE7QUFDQUM7QUFMeUI7QUFDUjtBQUNrRDtBQUtsRSxNQUFNRyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBRTtBQUVhLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHVDtJQUNDLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUFLQyxXQUFXWiwrSkFBZTtzQkFDOUIsNEVBQUNHLGtGQUFvQkE7MEJBQUVLOzs7Ozs7Ozs7Ozs7Ozs7O0FBSS9CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZnJvbnRlbmQvLi9zcmMvYXBwL2xheW91dC50c3g/NTdhOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSBcIm5leHRcIjtcbmltcG9ydCB7IEludGVyLCBMZXhlbmQgfSBmcm9tIFwibmV4dC9mb250L2dvb2dsZVwiO1xuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgXCIuL2dsb2JhbHMuY3NzXCI7XG5pbXBvcnQgeyBUaGVtZVByb3ZpZGVyV3JhcHBlciB9IGZyb20gXCIuL2NvbXBvbmVudHMvVGhlbWVQcm92aWRlcldyYXBwZXJcIjtcblxuY29uc3QgaW50ZXIgPSBJbnRlcih7IHN1YnNldHM6IFtcImxhdGluXCJdIH0pO1xuY29uc3QgbGV4ZW5kID0gTGV4ZW5kKHsgc3Vic2V0czogW1wibGF0aW5cIl0gfSk7XG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiBcIkFncm9TZXJ2aWNpb3NcIixcbiAgZGVzY3JpcHRpb246IFwiR2VzdGnDs24gZGUgU2VydmljaW9zIEFncm9wZWN1YXJpb3NcIixcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn0pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwiZXNcIj5cbiAgICAgIDxib2R5IGNsYXNzTmFtZT17aW50ZXIuY2xhc3NOYW1lfT5cbiAgICAgICAgPFRoZW1lUHJvdmlkZXJXcmFwcGVyPntjaGlsZHJlbn08L1RoZW1lUHJvdmlkZXJXcmFwcGVyPlxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJpbnRlciIsImxleGVuZCIsIlJlYWN0IiwiVGhlbWVQcm92aWRlcldyYXBwZXIiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiLCJjbGFzc05hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@mui","vendor-chunks/@emotion","vendor-chunks/prop-types","vendor-chunks/react-transition-group","vendor-chunks/stylis","vendor-chunks/hoist-non-react-statics","vendor-chunks/react-is","vendor-chunks/@babel","vendor-chunks/object-assign","vendor-chunks/clsx","vendor-chunks/framer-motion","vendor-chunks/@popperjs"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F(paginas)%2Fdashboard%2Fpage&page=%2F(paginas)%2Fdashboard%2Fpage&appPaths=%2F(paginas)%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2F(paginas)%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5CUSUARIO%5COneDrive%5CDocumentos%5CSpringBoot%5CServicios%5CFrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUSUARIO%5COneDrive%5CDocumentos%5CSpringBoot%5CServicios%5CFrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();