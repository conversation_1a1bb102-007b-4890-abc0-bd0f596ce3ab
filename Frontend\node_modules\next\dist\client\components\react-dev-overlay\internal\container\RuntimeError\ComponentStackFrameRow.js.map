{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/internal/container/RuntimeError/ComponentStackFrameRow.tsx"], "names": ["ComponentStackFrameRow", "EditorLink", "children", "componentStackFrame", "file", "column", "lineNumber", "open", "useOpenInEditor", "div", "tabIndex", "role", "onClick", "title", "svg", "xmlns", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "path", "d", "polyline", "points", "line", "x1", "y1", "x2", "y2", "formatLineNumber", "LocationLine", "SourceLocation", "canOpenInEditor", "span", "component", "data-nextjs-component-stack-frame", "h3"], "mappings": ";;;;+BAuFgBA;;;eAAAA;;;;;gEAvFE;iCAEc;AAEhC,SAASC,WAAW,KAMnB;IANmB,IAAA,EAClBC,QAAQ,EACRC,qBAAqB,EAAEC,IAAI,EAAEC,MAAM,EAAEC,UAAU,EAAE,EAIlD,GANmB;IAOlB,MAAMC,OAAOC,IAAAA,gCAAe,EAAC;QAC3BJ;QACAC;QACAC;IACF;IAEA,qBACE,sBAACG;QACCC,UAAU;QACVC,MAAM;QACNC,SAASL;QACTM,OAAO;;YAENX;0BACD,sBAACY;gBACCC,OAAM;gBACNC,SAAQ;gBACRC,MAAK;gBACLC,QAAO;gBACPC,aAAY;gBACZC,eAAc;gBACdC,gBAAe;;kCAEf,qBAACC;wBAAKC,GAAE;;kCACR,qBAACC;wBAASC,QAAO;;kCACjB,qBAACC;wBAAKC,IAAG;wBAAKC,IAAG;wBAAKC,IAAG;wBAAKC,IAAG;;;;;;AAIzC;AAEA,SAASC,iBAAiBzB,UAAkB,EAAED,MAA0B;IACtE,IAAI,CAACA,QAAQ;QACX,OAAOC;IACT;IAEA,OAAO,AAAGA,aAAW,MAAGD;AAC1B;AAEA,SAAS2B,aAAa,KAIrB;IAJqB,IAAA,EACpB7B,mBAAmB,EAGpB,GAJqB;IAKpB,MAAM,EAAEC,IAAI,EAAEE,UAAU,EAAED,MAAM,EAAE,GAAGF;IACrC,qBACE;;YACGC;YAAK;YAAEE,aAAa,AAAC,MAAGyB,iBAAiBzB,YAAYD,UAAQ,MAAK;;;AAGzE;AAEA,SAAS4B,eAAe,KAIvB;IAJuB,IAAA,EACtB9B,mBAAmB,EAGpB,GAJuB;IAKtB,MAAM,EAAEC,IAAI,EAAE8B,eAAe,EAAE,GAAG/B;IAElC,IAAIC,QAAQ8B,iBAAiB;QAC3B,qBACE,qBAACjC;YAAWE,qBAAqBA;sBAC/B,cAAA,qBAACgC;0BACC,cAAA,qBAACH;oBAAa7B,qBAAqBA;;;;IAI3C;IAEA,qBACE,qBAACM;kBACC,cAAA,qBAACuB;YAAa7B,qBAAqBA;;;AAGzC;AAEO,SAASH,uBAAuB,KAItC;IAJsC,IAAA,EACrCG,mBAAmB,EAGpB,GAJsC;IAKrC,MAAM,EAAEiC,SAAS,EAAE,GAAGjC;IAEtB,qBACE,sBAACM;QAAI4B,mCAAiC;;0BACpC,qBAACC;0BAAIF;;0BACL,qBAACH;gBAAe9B,qBAAqBA;;;;AAG3C"}