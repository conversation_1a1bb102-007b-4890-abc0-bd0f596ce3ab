{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/internal/helpers/use-websocket.ts"], "names": ["useCallback", "useContext", "useEffect", "useRef", "GlobalLayoutRouterContext", "getSocketUrl", "useWebsocket", "assetPrefix", "webSocketRef", "current", "url", "window", "WebSocket", "useSendMessage", "sendMessage", "data", "socket", "readyState", "OPEN", "send", "useTurbopack", "turbopackState", "init", "queue", "callback", "undefined", "processTurbopackMessage", "msg", "type", "startsWith", "push", "initCurrent", "then", "connect", "addMessageListener", "cb", "useWebsocketPing", "websocketRef", "tree", "interval", "setInterval", "JSON", "stringify", "event", "appDirRoute", "clearInterval"], "mappings": "AAAA,SAASA,WAAW,EAAEC,UAAU,EAAEC,SAAS,EAAEC,MAAM,QAAQ,QAAO;AAClE,SAASC,yBAAyB,QAAQ,8DAA6D;AACvG,SAASC,YAAY,QAAQ,mBAAkB;AAO/C,OAAO,SAASC,aAAaC,WAAmB;IAC9C,MAAMC,eAAeL;IAErBD,UAAU;QACR,IAAIM,aAAaC,OAAO,EAAE;YACxB;QACF;QAEA,MAAMC,MAAML,aAAaE;QAEzBC,aAAaC,OAAO,GAAG,IAAIE,OAAOC,SAAS,CAAC,AAAC,KAAEF,MAAI;IACrD,GAAG;QAACH;KAAY;IAEhB,OAAOC;AACT;AAEA,OAAO,SAASK,eAAeL,YAA6C;IAC1E,MAAMM,cAAcd,YAClB,CAACe;QACC,MAAMC,SAASR,aAAaC,OAAO;QACnC,IAAI,CAACO,UAAUA,OAAOC,UAAU,KAAKD,OAAOE,IAAI,EAAE;YAChD;QACF;QACA,OAAOF,OAAOG,IAAI,CAACJ;IACrB,GACA;QAACP;KAAa;IAEhB,OAAOM;AACT;AAEA,OAAO,SAASM,aAAaN,WAA8C;IACzE,MAAMO,iBAAiBlB,OAIpB;QACDmB,MAAM;QACN,0FAA0F;QAC1FC,OAAO,EAAE;QACTC,UAAUC;IACZ;IAEA,MAAMC,0BAA0B1B,YAAY,CAAC2B;YACtBA;QAArB,IAAI,UAAUA,SAAOA,YAAAA,IAAIC,IAAI,qBAARD,UAAUE,UAAU,CAAC,gBAAe;YACvD,MAAM,EAAEL,QAAQ,EAAED,KAAK,EAAE,GAAGF,eAAeZ,OAAO;YAClD,IAAIe,UAAU;gBACZA,SAASG;YACX,OAAO;gBACLJ,MAAOO,IAAI,CAACH;YACd;YACA,OAAO;QACT;QACA,OAAO;IACT,GAAG,EAAE;IAELzB,UAAU;QACR,MAAM,EAAEO,SAASsB,WAAW,EAAE,GAAGV;QACjC,2DAA2D;QAC3D,IAAIU,YAAYT,IAAI,EAAE;YACpB;QACF;QACAS,YAAYT,IAAI,GAAG;QAEnB,MAAM,CACJ,gGAAgG;QAChG,iEACAU,IAAI,CAAC;gBAAC,EAAEC,OAAO,EAAE;YACjB,MAAM,EAAExB,OAAO,EAAE,GAAGY;YACpBY,QAAQ;gBACNC,oBAAmBC,EAAmC;oBACpD1B,QAAQe,QAAQ,GAAGW;oBAEnB,iFAAiF;oBACjF,KAAK,MAAMR,OAAOlB,QAAQc,KAAK,CAAG;wBAChCY,GAAGR;oBACL;oBACAlB,QAAQc,KAAK,GAAGE;gBAClB;gBACAX;YACF;QACF;IACF,GAAG;QAACA;KAAY;IAEhB,OAAOY;AACT;AAEA,OAAO,SAASU,iBACdC,YAA6C;IAE7C,MAAMvB,cAAcD,eAAewB;IACnC,MAAM,EAAEC,IAAI,EAAE,GAAGrC,WAAWG;IAE5BF,UAAU;QACR,yCAAyC;QACzC,MAAMqC,WAAWC,YAAY;YAC3B1B,YACE2B,KAAKC,SAAS,CAAC;gBACbC,OAAO;gBACPL;gBACAM,aAAa;YACf;QAEJ,GAAG;QACH,OAAO,IAAMC,cAAcN;IAC7B,GAAG;QAACD;QAAMxB;KAAY;AACxB"}