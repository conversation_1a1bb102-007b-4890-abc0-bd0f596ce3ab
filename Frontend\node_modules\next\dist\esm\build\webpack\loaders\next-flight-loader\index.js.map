{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/next-flight-loader/index.ts"], "names": ["RSC_MOD_REF_PROXY_ALIAS", "BARREL_OPTIMIZATION_PREFIX", "RSC_MODULE_TYPES", "warnOnce", "getRSCModuleInformation", "formatBarrelOptimizedResource", "getModuleBuildInfo", "noopHeadPath", "require", "resolve", "MODULE_PROXY_PATH", "transformSource", "source", "sourceMap", "buildInfo", "Error", "_module", "rsc", "resourceKey", "resourcePath", "matchResource", "startsWith", "type", "client", "sourceType", "parser", "detectedClientEntryType", "clientEntryType", "clientRefs", "assumedSourceType", "length", "includes", "callback", "esmSource", "cnt", "ref", "replace"], "mappings": "AAAA,SAASA,uBAAuB,QAAQ,4BAA2B;AACnE,SACEC,0BAA0B,EAC1BC,gBAAgB,QACX,mCAAkC;AACzC,SAASC,QAAQ,QAAQ,yCAAwC;AACjE,SAASC,uBAAuB,QAAQ,yCAAwC;AAChF,SAASC,6BAA6B,QAAQ,cAAa;AAC3D,SAASC,kBAAkB,QAAQ,2BAA0B;AAE7D,MAAMC,eAAeC,QAAQC,OAAO,CAAC;AACrC,gEAAgE;AAChE,MAAMC,oBACJ;AAEF,eAAe,SAASC,gBAEtBC,MAAc,EACdC,SAAc;QAyBV,6BAAA,eAQAC,gBAiEAA;IAhGJ,8BAA8B;IAC9B,IAAI,OAAOF,WAAW,UAAU;QAC9B,MAAM,IAAIG,MAAM;IAClB;IAEA,gDAAgD;IAChD,mEAAmE;IACnE,MAAMD,YAAYR,mBAAmB,IAAI,CAACU,OAAO;IACjDF,UAAUG,GAAG,GAAGb,wBAAwBQ,QAAQ;IAEhD,2EAA2E;IAC3E,gFAAgF;IAChF,UAAU;IACV,EAAE;IACF,6EAA6E;IAC7E,iFAAiF;IACjF,gFAAgF;IAChF,iFAAiF;IACjF,uBAAuB;IACvB,EAAE;IACF,0EAA0E;IAC1E,sBAAsB;IACtB,IAAIM,cAAsB,IAAI,CAACC,YAAY;IAC3C,KAAI,gBAAA,IAAI,CAACH,OAAO,sBAAZ,8BAAA,cAAcI,aAAa,qBAA3B,4BAA6BC,UAAU,CAACpB,6BAA6B;QACvEiB,cAAcb,8BACZa,aACA,IAAI,CAACF,OAAO,CAACI,aAAa;IAE9B;IAEA,qBAAqB;IACrB,IAAIN,EAAAA,iBAAAA,UAAUG,GAAG,qBAAbH,eAAeQ,IAAI,MAAKpB,iBAAiBqB,MAAM,EAAE;YAChC,sBAAA;QAAnB,MAAMC,cAAa,iBAAA,IAAI,CAACR,OAAO,sBAAZ,uBAAA,eAAcS,MAAM,qBAApB,qBAAsBD,UAAU;QACnD,MAAME,0BAA0BZ,UAAUG,GAAG,CAACU,eAAe;QAC7D,MAAMC,aAAad,UAAUG,GAAG,CAACW,UAAU;QAE3C,4EAA4E;QAC5E,6EAA6E;QAC7E,4DAA4D;QAC5D,IAAIC,oBAAoBL;QACxB,IAAIK,sBAAsB,UAAUH,4BAA4B,QAAQ;YACtE,IACEE,WAAWE,MAAM,KAAK,KACrBF,WAAWE,MAAM,KAAK,KAAKF,UAAU,CAAC,EAAE,KAAK,IAC9C;gBACA,uEAAuE;gBACvE,yEAAyE;gBACzE,oBAAoB;gBACpBC,oBAAoB;YACtB,OAAO,IAAI,CAACD,WAAWG,QAAQ,CAAC,MAAM;gBACpC,2CAA2C;gBAC3CF,oBAAoB;YACtB;QACF;QAEA,IAAIA,sBAAsB,UAAU;YAClC,IAAID,WAAWG,QAAQ,CAAC,MAAM;gBAC5B,IAAI,CAACC,QAAQ,CACX,IAAIjB,MACF,CAAC,oGAAoG,CAAC;gBAG1G;YACF;YAEA,IAAIkB,YAAY,CAAC;6BACM,EAAEvB,kBAAkB;sCACX,EAAEQ,YAAY;;;;;;;;AAQpD,CAAC;YACK,IAAIgB,MAAM;YACV,KAAK,MAAMC,OAAOP,WAAY;gBAC5B,IAAIO,QAAQ,IAAI;oBACdF,aAAa,CAAC,wCAAwC,EAAEf,YAAY,KAAK,CAAC;gBAC5E,OAAO,IAAIiB,QAAQ,WAAW;oBAC5BF,aAAa,CAAC;;2BAEG,CAAC;gBACpB,OAAO;oBACLA,aAAa,CAAC;OACjB,EAAEC,IAAI,2BAA2B,EAAEhB,YAAY,CAAC,EAAEiB,IAAI;UACnD,EAAED,MAAM,IAAI,EAAEC,IAAI,GAAG,CAAC;gBACxB;YACF;YAEA,IAAI,CAACH,QAAQ,CAAC,MAAMC,WAAWpB;YAC/B;QACF;IACF;IAEA,IAAIC,EAAAA,kBAAAA,UAAUG,GAAG,qBAAbH,gBAAeQ,IAAI,MAAKpB,iBAAiBqB,MAAM,EAAE;QACnD,IAAIhB,iBAAiB,IAAI,CAACY,YAAY,EAAE;YACtChB,SACE,CAAC,0OAA0O,CAAC;QAEhP;IACF;IAEA,IAAI,CAAC6B,QAAQ,CACX,MACApB,OAAOwB,OAAO,CAACpC,yBAAyBU,oBACxCG;AAEJ"}