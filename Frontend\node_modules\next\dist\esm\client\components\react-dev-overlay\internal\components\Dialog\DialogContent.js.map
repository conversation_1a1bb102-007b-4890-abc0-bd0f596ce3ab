{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/internal/components/Dialog/DialogContent.tsx"], "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "className", "div", "data-nextjs-dialog-content"], "mappings": ";AAAA,YAAYA,WAAW,QAAO;AAO9B,MAAMC,gBAA8C,SAASA,cAAc,KAG1E;IAH0E,IAAA,EACzEC,QAAQ,EACRC,SAAS,EACV,GAH0E;IAIzE,qBACE,KAACC;QAAIC,4BAA0B;QAACF,WAAWA;kBACxCD;;AAGP;AAEA,SAASD,aAAa,GAAE"}