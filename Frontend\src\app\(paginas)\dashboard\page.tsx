// "use client";
// import React, { useEffect, useState } from "react";
// import { Grid, Card, Typography, Box, Skeleton } from "@mui/material";
// import { Timer } from "@mui/icons-material";
// import MetricCard from "../../components/cards/MetricCard";
// import WeatherWidget from "../../components/weather/WeatherWidget";
// import FarmSummary from "../../components/farm/FarmSummary";
// import TaskList from "../../components/tasks/TaskList";

// const Dashboard = () => {
//   // Estados para el formulario de eventos y visualización
//   const [selectedDate, setSelectedDate] = useState<string>("");
//   const [showEventForm, setShowEventForm] = useState(false);
//   const [showEvents, setShowEvents] = useState(false);
//   const [events, setEvents] = useState<
//     Array<{
//       id: string;
//       date: string;
//       title: string;
//       description: string;
//       time: string;
//     }>
//   >([]);

//   // Activar el estado de loading
//   const [loading, setLoading] = useState(true);

//   // Efecto para simular la carga de datos
//   useEffect(() => {
//     const fetchData = async () => {
//       setLoading(true);
//       try {
//         await new Promise((resolve) => setTimeout(resolve, 1500));
//       } catch (error) {
//         console.error("Error fetching data:", error);
//       } finally {
//         setLoading(false);
//       }
//     };

//     fetchData();
//   }, []);

//   // Función para manejar la selección de fecha
//   const handleDateClick = (date: string) => {
//     setSelectedDate(date);
//     setShowEventForm(true);
//     setShowEvents(false);
//   };

//   const formatearFecha = (fecha: Date) => {
//     const fechaFormateada = fecha.toLocaleDateString("es-ES", {
//       weekday: "long",
//       day: "numeric",
//       month: "long",
//       year: "numeric",
//     });

//     // Dividir la cadena en palabras
//     const palabras = fechaFormateada.split(" ");

//     // Capitalizar la primera letra del día y del mes
//     palabras[0] = palabras[0].charAt(0).toUpperCase() + palabras[0].slice(1); // día de la semana
//     palabras[3] = palabras[3].charAt(0).toUpperCase() + palabras[3].slice(1); // mes

//     // Unir las palabras de nuevo
//     return palabras.join(" ");
//   };

//   // Añadir esta función para determinar la estación actual
//   const obtenerEstacionActual = () => {
//     const fecha = new Date();
//     const mes = fecha.getMonth() + 1; // getMonth() devuelve 0-11
//     const dia = fecha.getDate();

//     // Verano: 21 de diciembre - 20 de marzo
//     if ((mes === 12 && dia >= 21) || mes <= 2 || (mes === 3 && dia <= 20)) {
//       return "Verano";
//     }
//     // Otoño: 21 de marzo - 20 de junio
//     else if ((mes === 3 && dia >= 21) || mes <= 5 || (mes === 6 && dia <= 20)) {
//       return "Otoño";
//     }
//     // Invierno: 21 de junio - 20 de septiembre
//     else if ((mes === 6 && dia >= 21) || mes <= 8 || (mes === 9 && dia <= 20)) {
//       return "Invierno";
//     }
//     // Primavera: 21 de septiembre - 20 de diciembre
//     else {
//       return "Primavera";
//     }
//   };

//   // Función para determinar el ciclo agrícola
//   const obtenerCicloAgricola = () => {
//     const estacion = obtenerEstacionActual();
//     return estacion === "Otoño" || estacion === "Invierno"
//       ? "Otoño-Invierno"
//       : "Primavera-Verano";
//   };

//   // Función para formatear la fecha actual
//   const formatearFechaActual = () => {
//     const fecha = new Date();
//     const fechaFormateada = fecha.toLocaleDateString("es-ES", {
//       weekday: "long",
//       day: "numeric",
//       month: "long",
//     });

//     // Dividir la cadena en palabras y capitalizar
//     const palabras = fechaFormateada.split(" ");
//     palabras[0] = palabras[0].charAt(0).toUpperCase() + palabras[0].slice(1); // día de la semana
//     palabras[3] = palabras[3].charAt(0).toUpperCase() + palabras[3].slice(1); // mes

//     return palabras.join(" ");
//   };

//   return (
//     <Box sx={{ padding: "16px" }}>
//       {/* Header Section */}
//       <Box sx={{ mb: 4 }}>
//         <Box
//           sx={{
//             display: "flex",
//             flexDirection: "column",
//             mb: 3,
//           }}
//         >
//           <Typography
//             variant="h6"
//             fontWeight="bold"
//             sx={{
//               color: "#2E7D32",
//               fontFamily: "Lexend, sans-serif",
//               fontSize: { xs: "1.3rem", sm: "1.6rem", md: "1.9rem" },
//               lineHeight: 1.2,
//               whiteSpace: "nowrap",
//               mb: 1,
//             }}
//           >
//             Bienvenido al Dashboard Agropecuario
//           </Typography>
//           <Typography
//             variant="subtitle1"
//             sx={{
//               color: "#666",
//               fontFamily: "Inter",
//               fontSize: { xs: "0.9rem", sm: "1rem", md: "1.1rem" },
//               lineHeight: 1.3,
//             }}
//           >
//             Gestiona sus Servicios de forma inteligente y eficiente
//           </Typography>
//         </Box>
//       </Box>

//       <Grid container spacing={3}>
//         {/* Sección de Cards */}
//         <Grid item xs={12}>
//           <Grid container spacing={3}>
//             <Grid item xs={12} sm={6} md={3}>
//               <MetricCard
//                 title="Total de Lotes"
//                 value={""}
//                 change="+5% desde ayer"
//                 icon="carduno.png"
//                 loading={loading}
//                 bgColor={""}
//                 hoverColor={""}
//               />
//             </Grid>
//             <Grid item xs={12} sm={6} md={3}>
//               <MetricCard
//                 title="Área Total"
//                 value={""}
//                 change="+3% desde la semana pasada"
//                 icon="carddos.png"
//                 loading={loading}
//                 bgColor={""}
//                 hoverColor={""}
//               />
//             </Grid>
//             <Grid item xs={12} sm={6} md={3}>
//               <MetricCard
//                 title="Total de Parcelas"
//                 value={""}
//                 change="-2% desde el trimestre pasado"
//                 icon="cardtres.png"
//                 loading={loading}
//                 bgColor={""}
//                 hoverColor={""}
//               />
//             </Grid>
//             <Grid item xs={12} sm={6} md={3}>
//               {loading ? (
//                 <Card
//                   sx={{
//                     backgroundColor: "#f8fafc",
//                     borderRadius: "16px",
//                     boxShadow: "0px 4px 12px rgba(0, 0, 0, 0.1)",
//                     padding: "16px",
//                   }}
//                 >
//                   <Box
//                     display="flex"
//                     justifyContent="space-between"
//                     alignItems="center"
//                     mb={2}
//                   >
//                     <Skeleton variant="text" width="50%" height={24} />
//                     <Skeleton variant="circular" width={40} height={40} />
//                   </Box>
//                   <Skeleton variant="text" width="70%" height={48} />
//                   <Skeleton
//                     variant="text"
//                     width="40%"
//                     height={24}
//                     sx={{ mt: 1 }}
//                   />
//                 </Card>
//               ) : (
//                 <Card
//                   sx={{
//                     padding: (theme) => theme.spacing(2),
//                     backgroundColor: "#ffffff",
//                     borderRadius: "8px",
//                     border: "1px solid #E5E7EB",
//                     boxShadow: "2px 2px 0px rgba(31, 142, 235, 0.2)",
//                     marginBottom: (theme) => theme.spacing(2),
//                     transition: "all 0.2s ease",
//                     "&:hover": {
//                       transform: "translate(-1px, -1px)",
//                       boxShadow: "3px 3px 0px rgba(31, 142, 235, 0.3)",
//                     },
//                   }}
//                 >
//                   <Box
//                     display="flex"
//                     justifyContent="space-between"
//                     alignItems="center"
//                     mb={2}
//                   >
//                     <Typography
//                       variant="body1"
//                       fontWeight="bold"
//                       sx={{
//                         color: "#000000",
//                         fontFamily: "Lexend, sans-serif",
//                       }}
//                     >
//                       Temporada Actual
//                     </Typography>
//                     <Box
//                       sx={{
//                         borderRadius: "50%",
//                         width: "40px",
//                         height: "40px",
//                         display: "flex",
//                         justifyContent: "center",
//                         alignItems: "center",
//                       }}
//                     >
//                       <Timer sx={{ color: "#2196F3", fontSize: "24px" }} />
//                     </Box>
//                   </Box>
//                   <Typography
//                     variant="h4"
//                     fontWeight="bold"
//                     sx={{ color: "#000000", fontFamily: "Inter" }}
//                   >
//                     {obtenerEstacionActual()}
//                   </Typography>
//                   <Typography
//                     variant="body2"
//                     sx={{
//                       color: "#666666",
//                       fontWeight: "600",
//                       mt: 1,
//                     }}
//                   >
//                     {obtenerCicloAgricola()}
//                   </Typography>
//                   <Typography
//                     variant="body2"
//                     sx={{
//                       color: "#888888",
//                       fontWeight: "500",
//                       mt: 0.5,
//                       fontSize: "0.85rem",
//                       fontFamily: "Inter",
//                     }}
//                   >
//                     {formatearFechaActual()}
//                   </Typography>
//                 </Card>
//               )}
//             </Grid>
//           </Grid>
//         </Grid>

//         {/* Sección de Farm Summary y Weather Widget */}
//         <Grid item xs={12}>
//           <Grid container spacing={3}>
//             <Grid item xs={12} md={8}>
//               {loading ? (
//                 <Box
//                   sx={{
//                     bgcolor: "#F1F8E9",
//                     p: 2,
//                     borderRadius: 2,
//                     border: "1px solid #C5E1A5",
//                     minHeight: "200px", // Altura mínima fija
//                     maxHeight: "fit-content", // Altura máxima adaptable
//                     display: "flex",
//                     flexDirection: "column",
//                   }}
//                 >
//                   {/* Header skeleton */}
//                   <Box
//                     sx={{
//                       display: "flex",
//                       justifyContent: "space-between",
//                       pb: 2,
//                       borderBottom: "1px solid #e0e0e0",
//                     }}
//                   >
//                     <Skeleton variant="text" width="40%" height={32} />
//                     <Skeleton variant="text" width="15%" height={24} />
//                   </Box>

//                   {/* Cards grid skeleton */}
//                   <Box sx={{ mt: 2, flex: 1 }}>
//                     <Grid container spacing={2}>
//                       {[1, 2, 3, 4, 5, 6].map((item) => (
//                         <Grid item xs={12} sm={6} md={4} key={item}>
//                           <Box
//                             sx={{
//                               p: 2,
//                               bgcolor: "#f8fafc",
//                               borderRadius: 2,
//                               height: "100px", // Altura fija para cada card
//                             }}
//                           >
//                             <Skeleton variant="text" width="80%" height={24} />
//                             <Skeleton
//                               variant="text"
//                               width="60%"
//                               height={20}
//                               sx={{ mt: 1 }}
//                             />
//                             <Skeleton
//                               variant="text"
//                               width="70%"
//                               height={20}
//                               sx={{ mt: 1 }}
//                             />
//                           </Box>
//                         </Grid>
//                       ))}
//                     </Grid>
//                   </Box>
//                 </Box>
//               ) : (
//                 <FarmSummary />
//               )}
//             </Grid>
//             <Grid item xs={12} md={4}>
//               {loading ? (
//                 <Box
//                   sx={{
//                     bgcolor: "#F1F8E9",
//                     p: 2,
//                     borderRadius: 2,
//                     border: "1px solid #C5E1A5",
//                     height: "100%",
//                     maxHeight: "400px", // Altura máxima fija
//                   }}
//                 >
//                   <Skeleton variant="text" width="60%" height={24} />
//                   <Skeleton variant="rectangular" height={200} sx={{ mt: 2 }} />
//                   <Box sx={{ mt: 2 }}>
//                     <Skeleton variant="text" width="40%" height={20} />
//                     <Skeleton variant="text" width="60%" height={20} />
//                     <Skeleton variant="text" width="80%" height={20} />
//                   </Box>
//                 </Box>
//               ) : (
//                 <WeatherWidget />
//               )}
//             </Grid>
//           </Grid>
//         </Grid>

//         {/* Sección de TaskList */}
//         <Grid item xs={12}>
//           <Grid container spacing={3}>
//             <Grid item xs={12} md={8}>
//               {loading ? (
//                 <Box
//                   sx={{
//                     bgcolor: "#F1F8E9",
//                     p: 2,
//                     borderRadius: 2,
//                     border: "1px solid #C5E1A5",
//                     height: "100%",
//                   }}
//                 >
//                   {/* Header skeleton */}
//                   <Box
//                     sx={{
//                       display: "flex",
//                       justifyContent: "space-between",
//                       pb: 2,
//                       borderBottom: "1px solid #e0e0e0",
//                     }}
//                   >
//                     <Skeleton variant="text" width="40%" height={32} />
//                     <Skeleton variant="text" width="15%" height={24} />
//                   </Box>

//                   {/* Tasks skeleton */}
//                   <Box sx={{ mt: 2 }}>
//                     {[1, 2, 3, 4, 5].map((item) => (
//                       <Box
//                         key={item}
//                         sx={{
//                           mb: 2,
//                           p: 2,
//                           bgcolor: "#f8fafc",
//                           borderRadius: 2,
//                         }}
//                       >
//                         <Box
//                           sx={{
//                             display: "flex",
//                             justifyContent: "space-between",
//                             alignItems: "flex-start",
//                           }}
//                         >
//                           <Box sx={{ display: "flex", gap: 1, width: "70%" }}>
//                             <Skeleton
//                               variant="circular"
//                               width={24}
//                               height={24}
//                             />
//                             <Box sx={{ flex: 1 }}>
//                               <Skeleton
//                                 variant="text"
//                                 width="80%"
//                                 height={24}
//                               />
//                               <Skeleton
//                                 variant="text"
//                                 width="60%"
//                                 height={20}
//                               />
//                             </Box>
//                           </Box>
//                           <Box
//                             sx={{
//                               display: "flex",
//                               flexDirection: "column",
//                               alignItems: "flex-end",
//                               gap: 1,
//                             }}
//                           >
//                             <Skeleton
//                               variant="rectangular"
//                               width={80}
//                               height={24}
//                               sx={{ borderRadius: 1 }}
//                             />
//                             <Skeleton variant="text" width={100} height={20} />
//                           </Box>
//                         </Box>
//                       </Box>
//                     ))}
//                   </Box>
//                 </Box>
//               ) : (
//                 <TaskList limit={5} />
//               )}
//             </Grid>
//           </Grid>
//         </Grid>
//       </Grid>
//     </Box>
//   );
// };

// export default Dashboard;


// Dashboard.jsx
import React from "react";
import { Box, Grid, Card, CardContent, Typography, IconButton } from "@mui/material";
import TrendingUpIcon from "@mui/icons-material/TrendingUp";
import CalendarTodayIcon from "@mui/icons-material/CalendarToday";
import LocalGasStationIcon from "@mui/icons-material/LocalGasStation";
import AttachMoneyIcon from "@mui/icons-material/AttachMoney";
import MapIcon from "@mui/icons-material/Map";
import BuildIcon from "@mui/icons-material/Build";

/** Simple KPI card */
function KpiCard({ title, value, sub, icon }) {
  return (
    <Card elevation={2}>
      <CardContent>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Box>
            <Typography variant="subtitle2" color="text.secondary">{title}</Typography>
            <Typography variant="h5" sx={{ mt: 0.5 }}>{value}</Typography>
            {sub && <Typography variant="caption" color="text.secondary">{sub}</Typography>}
          </Box>
          <Box>
            <IconButton size="small">{icon}</IconButton>
          </Box>
        </Box>
        {/* Aquí podrías poner un sparkline SVG o mini-chart */}
      </CardContent>
    </Card>
  );
}

export default function Dashboard({ data }) {
  // data puede venir de tu API: ingresos, horas, ha, mantenimientos, etc.
  // Estos son ejemplos mock.
  const kpIs = [
    { id: "ingresos", title: "Ingresos (mes)", value: `$ ${data?.ingresosMes ?? "0"}`, sub: "vs mes anterior: +8%", icon: <AttachMoneyIcon /> },
    { id: "hectareas", title: "Hectáreas atendidas", value: `${data?.hectareasMes ?? 0} ha`, sub: "Tipo: siembra/pulverización", icon: <MapIcon /> },
    { id: "utilizacion", title: "Utilización maquinaria", value: `${data?.utilizacion ?? 0}%`, sub: "Promedio flota", icon: <TrendingUpIcon /> },
    { id: "costohora", title: "Costo por hora", value: `$ ${data?.costoHora ?? 0}`, sub: "Comb, mano de obra, amort.", icon: <LocalGasStationIcon /> },
    { id: "ontime", title: "Trabajos a tiempo", value: `${data?.onTime ?? 0}%`, sub: "On-time completion", icon: <CalendarTodayIcon /> },
    { id: "mantenimiento", title: "Mantenimientos próximos", value: `${data?.mantProx ?? 0}`, sub: "Máquinas con servicio pendiente", icon: <BuildIcon /> },
  ];

  return (
    <Box p={2}>
      <Grid container spacing={2}>
        {kpIs.map(kpi => (
          <Grid item xs={12} sm={6} md={4} key={kpi.id}>
            <KpiCard {...kpi} />
          </Grid>
        ))}
      </Grid>

      <Box mt={3}>
        <Grid container spacing={2}>
          <Grid item xs={12} md={7}>
            <Card>
              <CardContent style={{ height: 360 }}>
                <Typography variant="h6">Mapa de trabajos (últimos 7 días)</Typography>
                {/* Aquí integra tu Mapbox component que pinta polígonos y muestra popups */}
                <Box mt={1}>[ComponenteMapboxAquí]</Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={5}>
            <Card>
              <CardContent>
                <Typography variant="h6">Alertas y próximos mantenimientos</Typography>
                <Box mt={1}>
                  {/* Lista de alertas */}
                  {(data?.alertas ?? []).map((a, i) => (
                    <Box key={i} mb={1}>
                      <Typography variant="body2">{a.text}</Typography>
                      <Typography variant="caption" color="text.secondary">{a.when}</Typography>
                    </Box>
                  ))}
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>
    </Box>
  );
}
