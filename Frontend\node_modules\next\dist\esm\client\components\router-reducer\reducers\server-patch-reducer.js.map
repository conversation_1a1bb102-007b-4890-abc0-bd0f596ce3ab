{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/server-patch-reducer.ts"], "names": ["createHrefFromUrl", "applyRouterStatePatchToTreeSkipDefault", "isNavigatingToNewRootLayout", "handleExternalUrl", "applyFlightData", "handleMutable", "createEmptyCacheNode", "handleSegmentMismatch", "serverPatchReducer", "state", "action", "flightData", "overrideCanonicalUrl", "mutable", "preserveCustomHistoryState", "pushRef", "pendingPush", "currentTree", "tree", "currentCache", "cache", "flightDataPath", "flightSegmentPath", "slice", "treePatch", "newTree", "canonicalUrl", "canonicalUrlOverrideHref", "undefined", "patchedTree"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,0BAAyB;AAC3D,SAASC,sCAAsC,QAAQ,sCAAqC;AAC5F,SAASC,2BAA2B,QAAQ,sCAAqC;AAOjF,SAASC,iBAAiB,QAAQ,qBAAoB;AACtD,SAASC,eAAe,QAAQ,uBAAsB;AACtD,SAASC,aAAa,QAAQ,oBAAmB;AAEjD,SAASC,oBAAoB,QAAQ,mBAAkB;AACvD,SAASC,qBAAqB,QAAQ,6BAA4B;AAElE,OAAO,SAASC,mBACdC,KAA2B,EAC3BC,MAAyB;IAEzB,MAAM,EAAEC,UAAU,EAAEC,oBAAoB,EAAE,GAAGF;IAE7C,MAAMG,UAAmB,CAAC;IAE1BA,QAAQC,0BAA0B,GAAG;IAErC,4DAA4D;IAC5D,IAAI,OAAOH,eAAe,UAAU;QAClC,OAAOR,kBACLM,OACAI,SACAF,YACAF,MAAMM,OAAO,CAACC,WAAW;IAE7B;IAEA,IAAIC,cAAcR,MAAMS,IAAI;IAC5B,IAAIC,eAAeV,MAAMW,KAAK;IAE9B,KAAK,MAAMC,kBAAkBV,WAAY;QACvC,mFAAmF;QACnF,MAAMW,oBAAoBD,eAAeE,KAAK,CAAC,GAAG,CAAC;QAEnD,MAAM,CAACC,UAAU,GAAGH,eAAeE,KAAK,CAAC,CAAC,GAAG,CAAC;QAC9C,MAAME,UAAUxB,uCACd,sBAAsB;QACtB;YAAC;eAAOqB;SAAkB,EAC1BL,aACAO;QAGF,IAAIC,YAAY,MAAM;YACpB,OAAOlB,sBAAsBE,OAAOC,QAAQc;QAC9C;QAEA,IAAItB,4BAA4Be,aAAaQ,UAAU;YACrD,OAAOtB,kBACLM,OACAI,SACAJ,MAAMiB,YAAY,EAClBjB,MAAMM,OAAO,CAACC,WAAW;QAE7B;QAEA,MAAMW,2BAA2Bf,uBAC7BZ,kBAAkBY,wBAClBgB;QAEJ,IAAID,0BAA0B;YAC5Bd,QAAQa,YAAY,GAAGC;QACzB;QAEA,MAAMP,QAAmBd;QACzBF,gBAAgBe,cAAcC,OAAOC;QAErCR,QAAQgB,WAAW,GAAGJ;QACtBZ,QAAQO,KAAK,GAAGA;QAEhBD,eAAeC;QACfH,cAAcQ;IAChB;IAEA,OAAOpB,cAAcI,OAAOI;AAC9B"}