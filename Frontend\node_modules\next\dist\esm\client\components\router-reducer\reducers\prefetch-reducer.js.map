{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/prefetch-reducer.ts"], "names": ["fetchServerResponse", "PrefetchKind", "prune<PERSON><PERSON><PERSON>tch<PERSON><PERSON>", "NEXT_RSC_UNION_QUERY", "PromiseQueue", "createPrefetchCacheKey", "prefetchQueue", "prefetchReducer", "state", "action", "prefetchCache", "url", "searchParams", "delete", "prefetchCache<PERSON>ey", "nextUrl", "cacheEntry", "get", "kind", "TEMPORARY", "set", "AUTO", "FULL", "serverResponse", "enqueue", "tree", "buildId", "treeAtTimeOfPrefetch", "data", "prefetchTime", "Date", "now", "lastUsedTime"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,2BAA0B;AAM9D,SAASC,YAAY,QAAQ,0BAAyB;AACtD,SAASC,kBAAkB,QAAQ,yBAAwB;AAC3D,SAASC,oBAAoB,QAAQ,2BAA0B;AAC/D,SAASC,YAAY,QAAQ,sBAAqB;AAClD,SAASC,sBAAsB,QAAQ,8BAA6B;AAEpE,OAAO,MAAMC,gBAAgB,IAAIF,aAAa,GAAE;AAEhD,OAAO,SAASG,gBACdC,KAA2B,EAC3BC,MAAsB;IAEtB,4DAA4D;IAC5DP,mBAAmBM,MAAME,aAAa;IAEtC,MAAM,EAAEC,GAAG,EAAE,GAAGF;IAChBE,IAAIC,YAAY,CAACC,MAAM,CAACV;IAExB,MAAMW,mBAAmBT,uBAAuBM,KAAKH,MAAMO,OAAO;IAClE,MAAMC,aAAaR,MAAME,aAAa,CAACO,GAAG,CAACH;IAE3C,IAAIE,YAAY;QACd;;;KAGC,GACD,IAAIA,WAAWE,IAAI,KAAKjB,aAAakB,SAAS,EAAE;YAC9CX,MAAME,aAAa,CAACU,GAAG,CAACN,kBAAkB;gBACxC,GAAGE,UAAU;gBACbE,MAAMT,OAAOS,IAAI;YACnB;QACF;QAEA;;;MAGE,GACF,IACE,CACEF,CAAAA,WAAWE,IAAI,KAAKjB,aAAaoB,IAAI,IACrCZ,OAAOS,IAAI,KAAKjB,aAAaqB,IAAI,AAAD,GAElC;YACA,OAAOd;QACT;IACF;IAEA,uGAAuG;IACvG,MAAMe,iBAAiBjB,cAAckB,OAAO,CAAC,IAC3CxB,oBACEW,KACA,mKAAmK;QACnKH,MAAMiB,IAAI,EACVjB,MAAMO,OAAO,EACbP,MAAMkB,OAAO,EACbjB,OAAOS,IAAI;IAIf,wEAAwE;IACxEV,MAAME,aAAa,CAACU,GAAG,CAACN,kBAAkB;QACxC,wEAAwE;QACxEa,sBAAsBnB,MAAMiB,IAAI;QAChCG,MAAML;QACNL,MAAMT,OAAOS,IAAI;QACjBW,cAAcC,KAAKC,GAAG;QACtBC,cAAc;IAChB;IAEA,OAAOxB;AACT"}