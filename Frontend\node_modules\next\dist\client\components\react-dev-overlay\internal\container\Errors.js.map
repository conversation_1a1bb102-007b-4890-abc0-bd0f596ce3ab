{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/internal/container/Errors.tsx"], "names": ["Errors", "styles", "getErrorSignature", "ev", "event", "type", "ACTION_UNHANDLED_ERROR", "ACTION_UNHANDLED_REJECTION", "reason", "name", "message", "stack", "_", "errors", "initialDisplayState", "versionInfo", "lookups", "setLookups", "React", "useState", "readyErrors", "nextError", "useMemo", "ready", "next", "idx", "length", "e", "id", "push", "prev", "isLoading", "Boolean", "useEffect", "mounted", "getErrorByType", "then", "resolved", "m", "displayState", "setDisplayState", "activeIdx", "setActiveIndex", "previous", "useCallback", "preventDefault", "v", "Math", "max", "min", "activeError", "minimize", "hide", "fullscreen", "Overlay", "Toast", "className", "onClick", "div", "svg", "xmlns", "width", "height", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "circle", "cx", "cy", "r", "line", "x1", "y1", "x2", "y2", "span", "button", "data-nextjs-toast-errors-hide-button", "stopPropagation", "aria-label", "CloseIcon", "isServerError", "includes", "getErrorSource", "error", "Dialog", "aria-<PERSON>by", "aria-<PERSON><PERSON>", "onClose", "undefined", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogHeader", "LeftRightDialogHeader", "close", "small", "VersionStalenessInfo", "h1", "p", "HotlinkedText", "text", "DialogBody", "RuntimeError", "toString", "css"], "mappings": ";;;;;;;;;;;;;;;IA0DaA,MAAM;eAANA;;IA6NAC,MAAM;eAANA;;;;;;iEAvRU;qCAIhB;wBAUA;uCAC+B;yBACd;uBACF;gCACS;iCAEA;8BACH;2BACF;8BACG;sCACQ;+BAEP;;;;;;;;;;AAgB9B,SAASC,kBAAkBC,EAAuB;IAChD,MAAM,EAAEC,KAAK,EAAE,GAAGD;IAClB,OAAQC,MAAMC,IAAI;QAChB,KAAKC,2CAAsB;QAC3B,KAAKC,+CAA0B;YAAE;gBAC/B,OAAO,AAAGH,MAAMI,MAAM,CAACC,IAAI,GAAC,OAAIL,MAAMI,MAAM,CAACE,OAAO,GAAC,OAAIN,MAAMI,MAAM,CAACG,KAAK;YAC7E;QACA;YAAS,CACT;IACF;IAEA,6DAA6D;IAC7D,MAAMC,IAAWR;IACjB,OAAO;AACT;AAEO,MAAMJ,SAAgC,SAASA,OAAO,KAI5D;IAJ4D,IAAA,EAC3Da,MAAM,EACNC,mBAAmB,EACnBC,WAAW,EACZ,GAJ4D;IAK3D,MAAM,CAACC,SAASC,WAAW,GAAGC,OAAMC,QAAQ,CAC1C,CAAC;IAGH,MAAM,CAACC,aAAaC,UAAU,GAAGH,OAAMI,OAAO,CAE5C;QACA,IAAIC,QAA2B,EAAE;QACjC,IAAIC,OAAmC;QAEvC,6DAA6D;QAC7D,IAAK,IAAIC,MAAM,GAAGA,MAAMZ,OAAOa,MAAM,EAAE,EAAED,IAAK;YAC5C,MAAME,IAAId,MAAM,CAACY,IAAI;YACrB,MAAM,EAAEG,EAAE,EAAE,GAAGD;YACf,IAAIC,MAAMZ,SAAS;gBACjBO,MAAMM,IAAI,CAACb,OAAO,CAACY,GAAG;gBACtB;YACF;YAEA,6BAA6B;YAC7B,IAAIH,MAAM,GAAG;gBACX,MAAMK,OAAOjB,MAAM,CAACY,MAAM,EAAE;gBAC5B,IAAIvB,kBAAkB4B,UAAU5B,kBAAkByB,IAAI;oBACpD;gBACF;YACF;YAEAH,OAAOG;YACP;QACF;QAEA,OAAO;YAACJ;YAAOC;SAAK;IACtB,GAAG;QAACX;QAAQG;KAAQ;IAEpB,MAAMe,YAAYb,OAAMI,OAAO,CAAU;QACvC,OAAOF,YAAYM,MAAM,GAAG,KAAKM,QAAQnB,OAAOa,MAAM;IACxD,GAAG;QAACb,OAAOa,MAAM;QAAEN,YAAYM,MAAM;KAAC;IAEtCR,OAAMe,SAAS,CAAC;QACd,IAAIZ,aAAa,MAAM;YACrB;QACF;QACA,IAAIa,UAAU;QAEdC,IAAAA,8BAAc,EAACd,WAAWe,IAAI,CAC5B,CAACC;YACC,sEAAsE;YACtE,uEAAuE;YACvE,kBAAkB;YAClB,IAAIH,SAAS;gBACXjB,WAAW,CAACqB,IAAO,CAAA;wBAAE,GAAGA,CAAC;wBAAE,CAACD,SAAST,EAAE,CAAC,EAAES;oBAAS,CAAA;YACrD;QACF,GACA;QACE,yCAAyC;QAC3C;QAGF,OAAO;YACLH,UAAU;QACZ;IACF,GAAG;QAACb;KAAU;IAEd,MAAM,CAACkB,cAAcC,gBAAgB,GACnCtB,OAAMC,QAAQ,CAAeL;IAC/B,MAAM,CAAC2B,WAAWC,eAAe,GAAGxB,OAAMC,QAAQ,CAAS;IAC3D,MAAMwB,WAAWzB,OAAM0B,WAAW,CAAC,CAACjB;QAClCA,qBAAAA,EAAGkB,cAAc;QACjBH,eAAe,CAACI,IAAMC,KAAKC,GAAG,CAAC,GAAGF,IAAI;IACxC,GAAG,EAAE;IACL,MAAMtB,OAAON,OAAM0B,WAAW,CAC5B,CAACjB;QACCA,qBAAAA,EAAGkB,cAAc;QACjBH,eAAe,CAACI,IACdC,KAAKC,GAAG,CAAC,GAAGD,KAAKE,GAAG,CAAC7B,YAAYM,MAAM,GAAG,GAAGoB,IAAI;IAErD,GACA;QAAC1B,YAAYM,MAAM;KAAC;QAIdN;IADR,MAAM8B,cAAchC,OAAMI,OAAO,CAC/B,IAAMF,CAAAA,yBAAAA,WAAW,CAACqB,UAAU,YAAtBrB,yBAA0B,MAChC;QAACqB;QAAWrB;KAAY;IAG1B,kEAAkE;IAClE,gDAAgD;IAChDF,OAAMe,SAAS,CAAC;QACd,IAAIpB,OAAOa,MAAM,GAAG,GAAG;YACrBT,WAAW,CAAC;YACZuB,gBAAgB;YAChBE,eAAe;QACjB;IACF,GAAG;QAAC7B,OAAOa,MAAM;KAAC;IAElB,MAAMyB,WAAWjC,OAAM0B,WAAW,CAAC,CAACjB;QAClCA,qBAAAA,EAAGkB,cAAc;QACjBL,gBAAgB;IAClB,GAAG,EAAE;IACL,MAAMY,OAAOlC,OAAM0B,WAAW,CAAC,CAACjB;QAC9BA,qBAAAA,EAAGkB,cAAc;QACjBL,gBAAgB;IAClB,GAAG,EAAE;IACL,MAAMa,aAAanC,OAAM0B,WAAW,CAClC,CAACjB;QACCA,qBAAAA,EAAGkB,cAAc;QACjBL,gBAAgB;IAClB,GACA,EAAE;IAGJ,2EAA2E;IAC3E,6CAA6C;IAC7C,IAAI3B,OAAOa,MAAM,GAAG,KAAKwB,eAAe,MAAM;QAC5C,OAAO;IACT;IAEA,IAAInB,WAAW;QACb,6BAA6B;QAC7B,qBAAO,qBAACuB,gBAAO;IACjB;IAEA,IAAIf,iBAAiB,UAAU;QAC7B,OAAO;IACT;IAEA,IAAIA,iBAAiB,aAAa;QAChC,qBACE,qBAACgB,YAAK;YAACC,WAAU;YAA6BC,SAASJ;sBACrD,cAAA,sBAACK;gBAAIF,WAAU;;kCACb,sBAACG;wBACCC,OAAM;wBACNC,OAAM;wBACNC,QAAO;wBACPC,SAAQ;wBACRC,MAAK;wBACLC,QAAO;wBACPC,aAAY;wBACZC,eAAc;wBACdC,gBAAe;;0CAEf,qBAACC;gCAAOC,IAAG;gCAAKC,IAAG;gCAAKC,GAAE;;0CAC1B,qBAACC;gCAAKC,IAAG;gCAAKC,IAAG;gCAAIC,IAAG;gCAAKC,IAAG;;0CAChC,qBAACJ;gCAAKC,IAAG;gCAAKC,IAAG;gCAAKC,IAAG;gCAAQC,IAAG;;;;kCAEtC,sBAACC;;4BACE1D,YAAYM,MAAM;4BAAC;4BAAON,YAAYM,MAAM,GAAG,IAAI,MAAM;;;kCAE5D,qBAACqD;wBACCC,sCAAoC;wBACpCxB,WAAU;wBACVnD,MAAK;wBACLoD,SAAS,CAAC9B;4BACRA,EAAEsD,eAAe;4BACjB7B;wBACF;wBACA8B,cAAW;kCAEX,cAAA,qBAACC,oBAAS;;;;;IAKpB;IAEA,MAAMC,gBAAgB;QAAC;QAAU;KAAc,CAACC,QAAQ,CACtDC,IAAAA,+BAAc,EAACpC,YAAYqC,KAAK,KAAK;IAGvC,qBACE,qBAACjC,gBAAO;kBACN,cAAA,qBAACkC,cAAM;YACLnF,MAAK;YACLoF,mBAAgB;YAChBC,oBAAiB;YACjBC,SAASP,gBAAgBQ,YAAYzC;sBAErC,cAAA,sBAAC0C,qBAAa;;kCACZ,sBAACC,oBAAY;wBAACtC,WAAU;;0CACtB,sBAACuC,4CAAqB;gCACpBpD,UAAUF,YAAY,IAAIE,WAAW;gCACrCnB,MAAMiB,YAAYrB,YAAYM,MAAM,GAAG,IAAIF,OAAO;gCAClDwE,OAAOZ,gBAAgBQ,YAAYzC;;kDAEnC,sBAAC8C;;0DACC,qBAACnB;0DAAMrC,YAAY;;4CAAS;4CAAI;0DAChC,qBAACqC;0DAAM1D,YAAYM,MAAM;;4CAAQ;4CAChCN,YAAYM,MAAM,GAAG,IAAI,KAAK;;;oCAEhCX,4BAAc,qBAACmF,0CAAoB;wCAAE,GAAGnF,WAAW;yCAAO;;;0CAE7D,qBAACoF;gCAAGvE,IAAG;0CACJwD,gBAAgB,iBAAiB;;0CAEpC,sBAACgB;gCAAExE,IAAG;;oCACHsB,YAAYqC,KAAK,CAAC9E,IAAI;oCAAC;oCAAE;kDAC1B,qBAAC4F,4BAAa;wCAACC,MAAMpD,YAAYqC,KAAK,CAAC7E,OAAO;;;;4BAE/C0E,8BACC,qBAAC1B;0CACC,cAAA,qBAACuC;8CAAM;;iCAKPL;;;kCAEN,qBAACW,kBAAU;wBAAC/C,WAAU;kCACpB,cAAA,qBAACgD,0BAAY;4BAAiCjB,OAAOrC;2BAAlCA,YAAYtB,EAAE,CAAC6E,QAAQ;;;;;;AAMtD;AAEO,MAAMxG,aAASyG,kBAAG"}