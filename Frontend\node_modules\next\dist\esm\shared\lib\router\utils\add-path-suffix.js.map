{"version": 3, "sources": ["../../../../../src/shared/lib/router/utils/add-path-suffix.ts"], "names": ["parsePath", "addPathSuffix", "path", "suffix", "startsWith", "pathname", "query", "hash"], "mappings": "AAAA,SAASA,SAAS,QAAQ,eAAc;AAExC;;;;CAIC,GACD,OAAO,SAASC,cAAcC,IAAY,EAAEC,MAAe;IACzD,IAAI,CAACD,KAAKE,UAAU,CAAC,QAAQ,CAACD,QAAQ;QACpC,OAAOD;IACT;IAEA,MAAM,EAAEG,QAAQ,EAAEC,KAAK,EAAEC,IAAI,EAAE,GAAGP,UAAUE;IAC5C,OAAO,AAAC,KAAEG,WAAWF,SAASG,QAAQC;AACxC"}