{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/internal/helpers/launchEditor.ts"], "names": ["cyan", "green", "red", "child_process", "fs", "os", "path", "shellQuote", "isTerminalEditor", "editor", "COMMON_EDITORS_MACOS", "COMMON_EDITORS_LINUX", "atom", "Brackets", "code", "vscodium", "emacs", "gvim", "sublime_text", "vim", "COMMON_EDITORS_WIN", "WINDOWS_FILE_NAME_ACCESS_LIST", "getArgumentsForLineNumber", "fileName", "lineNumber", "colNumber", "editorBasename", "basename", "replace", "toString", "guessEditor", "process", "env", "REACT_EDITOR", "parse", "platform", "output", "execSync", "processNames", "Object", "keys", "i", "length", "processName", "indexOf", "runningProcesses", "split", "processPath", "trim", "error", "VISUAL", "EDITOR", "printInstructions", "errorMessage", "console", "log", "launchEditor", "existsSync", "Number", "isInteger", "args", "toLowerCase", "startsWith", "test", "release", "relative", "concat", "push", "p", "undefined", "spawn", "stdio", "detached", "quote", "on", "errorCode", "message"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;CAsBC,GACD,SAASA,IAAI,EAAEC,KAAK,EAAEC,GAAG,QAAQ,gCAA+B;AAChE,OAAOC,mBAAmB,gBAAe;AACzC,OAAOC,QAAQ,KAAI;AACnB,OAAOC,QAAQ,KAAI;AACnB,OAAOC,UAAU,OAAM;AACvB,aAAa;AACb,OAAOC,gBAAgB,iCAAgC;AAEvD,SAASC,iBAAiBC,MAAc;IACtC,OAAQA;QACN,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YAAQ;gBACX,OAAO;YACT;QACA;YAAS,CACT;IACF;IACA,OAAO;AACT;AAEA,+DAA+D;AAC/D,+EAA+E;AAC/E,wBAAwB;AACxB,MAAMC,uBAAuB;IAC3B,8CAA8C;IAC9C,wDACE;IACF,sDAAsD;IACtD,8DACE;IACF,kEACE;IACF,kEACE;IACF,gEACE;IACF,2EACE;IACF,sDACE;IACF,oDACE;IACF,gDACE;IACF,uDACE;IACF,sDACE;IACF,oDACE;IACF,uDACE;IACF,sDACE;IACF,sDACE;IACF,kDAAkD;IAClD,kDACE;IACF,gDACE;AACJ;AAEA,MAAMC,uBAAuB;IAC3BC,MAAM;IACNC,UAAU;IACVC,MAAM;IACN,iBAAiB;IACjBC,UAAU;IACVC,OAAO;IACPC,MAAM;IACN,WAAW;IACX,eAAe;IACf,cAAc;IACd,eAAe;IACfC,cAAc;IACdC,KAAK;IACL,eAAe;IACf,aAAa;IACb,YAAY;AACd;AAEA,MAAMC,qBAAqB;IACzB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,kEAAkE;AAClE,6EAA6E;AAC7E,sEAAsE;AACtE,MAAMC,gCACJ;AAEF,SAASC,0BACPb,MAAc,EACdc,QAAgB,EAChBC,UAAkB,EAClBC,SAAiB;IAEjB,MAAMC,iBAAiBpB,KAAKqB,QAAQ,CAAClB,QAAQmB,OAAO,CAAC,qBAAqB;IAC1E,OAAQF;QACN,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YAAgB;gBACnB,OAAO;oBAACH,WAAW,MAAMC,aAAa,MAAMC;iBAAU;YACxD;QACA,KAAK;QACL,KAAK;YAAS;gBACZ,OAAO;oBAACF,WAAW,MAAMC;iBAAW;YACtC;QACA,KAAK;YAAa;gBAChB,OAAO;oBAAC,OAAOA;oBAAY,OAAOC;oBAAWF;iBAAS;YACxD;QACA,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YAAQ;gBACX,OAAO;oBAAC,MAAMC;oBAAYD;iBAAS;YACrC;QACA,KAAK;QACL,KAAK;YAAe;gBAClB,OAAO;oBAAC,MAAMC,aAAa,MAAMC;oBAAWF;iBAAS;YACvD;QACA,KAAK;QACL,KAAK;QACL,KAAK;YAAQ;gBACX,OAAO;oBAAC;oBAAUC,WAAWK,QAAQ;oBAAIN;iBAAS;YACpD;QACA,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YAAY;gBACf,OAAO;oBAAC;oBAAMA,WAAW,MAAMC,aAAa,MAAMC;iBAAU;YAC9D;QACA,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YAAW;gBACd,OAAO;oBAAC;oBAAUD,WAAWK,QAAQ;oBAAIN;iBAAS;YACpD;QACA;YAAS;gBACP,oDAAoD;gBACpD,8DAA8D;gBAC9D,8CAA8C;gBAC9C,OAAO;oBAACA;iBAAS;YACnB;IACF;AACF;AAEA,SAASO;IACP,8BAA8B;IAC9B,IAAIC,QAAQC,GAAG,CAACC,YAAY,EAAE;QAC5B,OAAO1B,WAAW2B,KAAK,CAACH,QAAQC,GAAG,CAACC,YAAY;IAClD;IAEA,wDAAwD;IACxD,4BAA4B;IAC5B,2BAA2B;IAC3B,IAAI;QACF,IAAIF,QAAQI,QAAQ,KAAK,UAAU;YACjC,MAAMC,SAASjC,cAAckC,QAAQ,CAAC,QAAQR,QAAQ;YACtD,MAAMS,eAAeC,OAAOC,IAAI,CAAC9B;YACjC,IAAK,IAAI+B,IAAI,GAAGA,IAAIH,aAAaI,MAAM,EAAED,IAAK;gBAC5C,MAAME,cAAcL,YAAY,CAACG,EAAE;gBACnC,IAAIL,OAAOQ,OAAO,CAACD,iBAAiB,CAAC,GAAG;oBACtC,OAAO;wBAAEjC,oBAA4B,CAACiC,YAAY;qBAAC;gBACrD;YACF;QACF,OAAO,IAAIZ,QAAQI,QAAQ,KAAK,SAAS;YACvC,kEAAkE;YAClE,wEAAwE;YACxE,MAAMC,SAASjC,cACZkC,QAAQ,CACP,sEAEDR,QAAQ;YACX,MAAMgB,mBAAmBT,OAAOU,KAAK,CAAC;YACtC,IAAK,IAAIL,IAAI,GAAGA,IAAII,iBAAiBH,MAAM,EAAED,IAAK;gBAChD,MAAMM,cAAcF,gBAAgB,CAACJ,EAAE,CAACO,IAAI;gBAC5C,MAAML,cAAcrC,KAAKqB,QAAQ,CAACoB;gBAClC,IAAI3B,mBAAmBwB,OAAO,CAACD,iBAAiB,CAAC,GAAG;oBAClD,OAAO;wBAACI;qBAAY;gBACtB;YACF;QACF,OAAO,IAAIhB,QAAQI,QAAQ,KAAK,SAAS;YACvC,8BAA8B;YAC9B,oCAAoC;YACpC,iCAAiC;YACjC,MAAMC,SAASjC,cACZkC,QAAQ,CAAC,yCACTR,QAAQ;YACX,MAAMS,eAAeC,OAAOC,IAAI,CAAC7B;YACjC,IAAK,IAAI8B,IAAI,GAAGA,IAAIH,aAAaI,MAAM,EAAED,IAAK;gBAC5C,MAAME,cAAcL,YAAY,CAACG,EAAE;gBACnC,IAAIL,OAAOQ,OAAO,CAACD,iBAAiB,CAAC,GAAG;oBACtC,OAAO;wBAAEhC,oBAA4B,CAACgC,YAAY;qBAAW;gBAC/D;YACF;QACF;IACF,EAAE,OAAOM,OAAO;IACd,YAAY;IACd;IAEA,sCAAsC;IACtC,IAAIlB,QAAQC,GAAG,CAACkB,MAAM,EAAE;QACtB,OAAO;YAACnB,QAAQC,GAAG,CAACkB,MAAM;SAAC;IAC7B,OAAO,IAAInB,QAAQC,GAAG,CAACmB,MAAM,EAAE;QAC7B,OAAO;YAACpB,QAAQC,GAAG,CAACmB,MAAM;SAAC;IAC7B;IAEA,OAAO,EAAE;AACX;AAEA,SAASC,kBAAkB7B,QAAgB,EAAE8B,YAA2B;IACtEC,QAAQC,GAAG;IACXD,QAAQC,GAAG,CACTrD,IAAI,oBAAoBI,KAAKqB,QAAQ,CAACJ,YAAY;IAEpD,IAAI8B,cAAc;QAChB,IAAIA,YAAY,CAACA,aAAaX,MAAM,GAAG,EAAE,KAAK,KAAK;YACjDW,gBAAgB;QAClB;QACAC,QAAQC,GAAG,CAACrD,IAAI,8CAA8CmD;IAChE;IACAC,QAAQC,GAAG;IACXD,QAAQC,GAAG,CACT,0DACEvD,KAAK,uBACL,aACAC,MAAM,gBACN,kCACA;IAEJqD,QAAQC,GAAG;AACb;AAEA,SAASC,aAAajC,QAAgB,EAAEC,UAAkB,EAAEC,SAAiB;IAC3E,IAAI,CAACrB,GAAGqD,UAAU,CAAClC,WAAW;QAC5B;IACF;IAEA,wDAAwD;IACxD,8GAA8G;IAC9G,sCAAsC;IACtC,IAAI,CAAEmC,CAAAA,OAAOC,SAAS,CAACnC,eAAeA,aAAa,CAAA,GAAI;QACrD;IACF;IAEA,8DAA8D;IAC9D,eAAe;IACf,IAAI,CAAEkC,CAAAA,OAAOC,SAAS,CAAClC,cAAcA,YAAY,CAAA,GAAI;QACnDA,YAAY;IACd;IAEA,IAAI,CAAChB,QAAQ,GAAGmD,KAAK,GAAG9B;IAExB,IAAI,CAACrB,QAAQ;QACX2C,kBAAkB7B,UAAU;QAC5B;IACF;IAEA,IAAId,OAAOoD,WAAW,OAAO,QAAQ;QACnC;IACF;IAEA,IACE9B,QAAQI,QAAQ,KAAK,WACrBZ,SAASuC,UAAU,CAAC,YACpB,aAAaC,IAAI,CAAC1D,GAAG2D,OAAO,KAC5B;QACA,8DAA8D;QAC9D,mDAAmD;QACnD,gEAAgE;QAChE,kGAAkG;QAClG,gEAAgE;QAChE,oEAAoE;QACpEzC,WAAWjB,KAAK2D,QAAQ,CAAC,IAAI1C;IAC/B;IAEA,2EAA2E;IAC3E,+EAA+E;IAC/E,4EAA4E;IAC5E,uEAAuE;IACvE,IACEQ,QAAQI,QAAQ,KAAK,WACrB,CAACd,8BAA8B0C,IAAI,CAACxC,SAASyB,IAAI,KACjD;QACAM,QAAQC,GAAG;QACXD,QAAQC,GAAG,CACTrD,IAAI,oBAAoBI,KAAKqB,QAAQ,CAACJ,YAAY;QAEpD+B,QAAQC,GAAG;QACXD,QAAQC,GAAG,CACT,4EACE,sEACA,uEACA;QAEJD,QAAQC,GAAG;QACX;IACF;IAEA,IAAI/B,YAAY;QACdoC,OAAOA,KAAKM,MAAM,CAChB5C,0BAA0Bb,QAAQc,UAAUC,YAAYC;IAE5D,OAAO;QACLmC,KAAKO,IAAI,CAAC5C;IACZ;IAEA,IAAI6C,IAA4CC;IAChD,IAAItC,QAAQI,QAAQ,KAAK,SAAS;QAChC,kEAAkE;QAClE,qBAAqB;QACrBiC,IAAIjE,cAAcmE,KAAK,CAAC,WAAW;YAAC;YAAM7D;SAAO,CAACyD,MAAM,CAACN,OAAO;YAC9DW,OAAO;YACPC,UAAU;QACZ;IACF,OAAO,IAAIhE,iBAAiBC,SAAS;QACnC,IAAIsB,QAAQI,QAAQ,KAAK,UAAU;YACjCiC,IAAIjE,cAAcmE,KAAK,CACrB,aACA;gBACE;gBACC,+CAA4C/D,WAAWkE,KAAK,CAAC;oBAC5DhE;uBACGmD;iBACJ,IAAE;aACJ,EACD;gBAAEW,OAAO;YAAS;QAEtB,OAAO;YACLnB,kBAAkB7B,UAAU;QAC9B;IACF,OAAO;QACL6C,IAAIjE,cAAcmE,KAAK,CAAC7D,QAAQmD,MAAM;YAAEW,OAAO;QAAU;IAC3D;IAEA,IAAIH,GAAG;QACLA,EAAEM,EAAE,CAAC,QAAQ,SAAUC,SAAS;YAC9B,IAAIA,WAAW;gBACbvB,kBAAkB7B,UAAU,WAAWoD,YAAY;YACrD;QACF;QACAP,EAAEM,EAAE,CAAC,SAAS,SAAUzB,KAAK;YAC3BG,kBAAkB7B,UAAU0B,MAAM2B,OAAO;QAC3C;IACF;AACF;AAEA,SAASpB,YAAY,GAAE"}