{"c": ["app/(paginas)/dashboard/page", "webpack"], "r": [], "m": ["(app-pages-browser)/./node_modules/@mui/icons-material/AcUnit.js", "(app-pages-browser)/./node_modules/@mui/icons-material/AccessTime.js", "(app-pages-browser)/./node_modules/@mui/icons-material/Air.js", "(app-pages-browser)/./node_modules/@mui/icons-material/ArrowForward.js", "(app-pages-browser)/./node_modules/@mui/icons-material/Assignment.js", "(app-pages-browser)/./node_modules/@mui/icons-material/CalendarToday.js", "(app-pages-browser)/./node_modules/@mui/icons-material/CheckCircleOutline.js", "(app-pages-browser)/./node_modules/@mui/icons-material/Cloud.js", "(app-pages-browser)/./node_modules/@mui/icons-material/CloudQueue.js", "(app-pages-browser)/./node_modules/@mui/icons-material/Home.js", "(app-pages-browser)/./node_modules/@mui/icons-material/LocationOn.js", "(app-pages-browser)/./node_modules/@mui/icons-material/Opacity.js", "(app-pages-browser)/./node_modules/@mui/icons-material/PendingOutlined.js", "(app-pages-browser)/./node_modules/@mui/icons-material/Person.js", "(app-pages-browser)/./node_modules/@mui/icons-material/Refresh.js", "(app-pages-browser)/./node_modules/@mui/icons-material/Thermostat.js", "(app-pages-browser)/./node_modules/@mui/icons-material/Umbrella.js", "(app-pages-browser)/./node_modules/@mui/icons-material/Visibility.js", "(app-pages-browser)/./node_modules/@mui/icons-material/WbSunny.js", "(app-pages-browser)/./node_modules/@mui/icons-material/esm/Timer.js", "(app-pages-browser)/./node_modules/@mui/material/Card/Card.js", "(app-pages-browser)/./node_modules/@mui/material/Card/cardClasses.js", "(app-pages-browser)/./node_modules/@mui/material/Chip/Chip.js", "(app-pages-browser)/./node_modules/@mui/material/Chip/chipClasses.js", "(app-pages-browser)/./node_modules/@mui/material/CircularProgress/CircularProgress.js", "(app-pages-browser)/./node_modules/@mui/material/CircularProgress/circularProgressClasses.js", "(app-pages-browser)/./node_modules/@mui/material/Grid/Grid.js", "(app-pages-browser)/./node_modules/@mui/material/Grid/GridContext.js", "(app-pages-browser)/./node_modules/@mui/material/Grid/gridClasses.js", "(app-pages-browser)/./node_modules/@mui/material/Link/Link.js", "(app-pages-browser)/./node_modules/@mui/material/Link/getTextDecoration.js", "(app-pages-browser)/./node_modules/@mui/material/Link/linkClasses.js", "(app-pages-browser)/./node_modules/@mui/material/Skeleton/Skeleton.js", "(app-pages-browser)/./node_modules/@mui/material/Skeleton/skeletonClasses.js", "(app-pages-browser)/./node_modules/@mui/material/ToggleButton/ToggleButton.js", "(app-pages-browser)/./node_modules/@mui/material/ToggleButton/toggleButtonClasses.js", "(app-pages-browser)/./node_modules/@mui/material/ToggleButtonGroup/ToggleButtonGroup.js", "(app-pages-browser)/./node_modules/@mui/material/ToggleButtonGroup/ToggleButtonGroupButtonContext.js", "(app-pages-browser)/./node_modules/@mui/material/ToggleButtonGroup/ToggleButtonGroupContext.js", "(app-pages-browser)/./node_modules/@mui/material/ToggleButtonGroup/isValueSelected.js", "(app-pages-browser)/./node_modules/@mui/material/ToggleButtonGroup/toggleButtonGroupClasses.js", "(app-pages-browser)/./node_modules/@mui/material/internal/svg-icons/Cancel.js", "(app-pages-browser)/./node_modules/@mui/material/styles/cssUtils.js", "(app-pages-browser)/./node_modules/@mui/system/esm/colorManipulator.js", "(app-pages-browser)/./node_modules/@mui/utils/esm/getValidReactChildren/getValidReactChildren.js", "(app-pages-browser)/./src/app/components/cards/MetricCard.tsx", "(app-pages-browser)/./src/app/components/farm/FarmSummary.tsx", "(app-pages-browser)/./src/app/components/tasks/TaskList.tsx", "(app-pages-browser)/./src/app/components/weather/WeatherWidget.tsx"]}