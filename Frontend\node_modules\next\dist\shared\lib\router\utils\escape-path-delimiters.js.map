{"version": 3, "sources": ["../../../../../src/shared/lib/router/utils/escape-path-delimiters.ts"], "names": ["escapePathDelimiters", "segment", "escapeEncoded", "replace", "RegExp", "char", "encodeURIComponent"], "mappings": "AAAA,2CAA2C;;;;;+BAC3C;;;eAAwBA;;;AAAT,SAASA,qBACtBC,OAAe,EACfC,aAAuB;IAEvB,OAAOD,QAAQE,OAAO,CACpB,IAAIC,OAAO,AAAC,WAAQF,CAAAA,gBAAgB,iBAAiB,EAAC,IAAE,KAAI,OAC5D,CAACG,OAAiBC,mBAAmBD;AAEzC"}