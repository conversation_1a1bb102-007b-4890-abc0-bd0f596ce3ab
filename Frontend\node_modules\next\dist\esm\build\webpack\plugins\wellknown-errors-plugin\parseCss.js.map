{"version": 3, "sources": ["../../../../../src/build/webpack/plugins/wellknown-errors-plugin/parseCss.ts"], "names": ["bold", "cyan", "red", "yellow", "SimpleWebpackError", "regexCssError", "getCssError", "fileName", "err", "name", "stack", "SyntaxError", "res", "exec", "message", "_lineNumber", "_column", "reason", "lineNumber", "Math", "max", "parseInt", "column", "toString", "concat"], "mappings": "AAAA,SAASA,IAAI,EAAEC,IAAI,EAAEC,GAAG,EAAEC,MAAM,QAAQ,6BAA4B;AACpE,SAASC,kBAAkB,QAAQ,uBAAsB;AAEzD,MAAMC,gBACJ;AAEF,OAAO,SAASC,YACdC,QAAgB,EAChBC,GAAU;IAEV,IACE,CACE,CAAA,AAACA,CAAAA,IAAIC,IAAI,KAAK,oBAAoBD,IAAIC,IAAI,KAAK,aAAY,KAC3D,AAACD,IAAYE,KAAK,KAAK,SACvB,CAAEF,CAAAA,eAAeG,WAAU,CAAC,GAE9B;QACA,OAAO;IACT;IAEA,uGAAuG;IAEvG,MAAMC,MAAMP,cAAcQ,IAAI,CAACL,IAAIM,OAAO;IAC1C,IAAIF,KAAK;QACP,MAAM,GAAGG,aAAaC,SAASC,OAAO,GAAGL;QACzC,MAAMM,aAAaC,KAAKC,GAAG,CAAC,GAAGC,SAASN,aAAa;QACrD,MAAMO,SAASH,KAAKC,GAAG,CAAC,GAAGC,SAASL,SAAS;QAE7C,OAAO,IAAIZ,mBACT,CAAC,EAAEH,KAAKM,UAAU,CAAC,EAAEJ,OAAOe,WAAWK,QAAQ,IAAI,CAAC,EAAEpB,OACpDmB,OAAOC,QAAQ,IACf,CAAC,EACHrB,IAAIF,KAAK,iBAAiBwB,MAAM,CAAC,CAAC,EAAE,EAAEP,OAAO,CAAC;IAElD;IAEA,OAAO;AACT"}