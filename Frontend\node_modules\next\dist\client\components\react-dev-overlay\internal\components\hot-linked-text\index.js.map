{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/internal/components/hot-linked-text/index.tsx"], "names": ["HotlinkedText", "linkRegex", "props", "text", "wordsAndWhitespaces", "getWordsAndWhitespaces", "test", "map", "word", "index", "React", "Fragment", "a", "href"], "mappings": ";;;;+BAKaA;;;eAAAA;;;;;gEALK;wCACqB;AAEvC,MAAMC,YAAY;AAEX,MAAMD,gBAER,SAASA,cAAcE,KAAK;IAC/B,MAAM,EAAEC,IAAI,EAAE,GAAGD;IAEjB,MAAME,sBAAsBC,IAAAA,8CAAsB,EAACF;IAEnD,qBACE;kBACGF,UAAUK,IAAI,CAACH,QACZC,oBAAoBG,GAAG,CAAC,CAACC,MAAMC;YAC7B,IAAIR,UAAUK,IAAI,CAACE,OAAO;gBACxB,qBACE,qBAACE,cAAK,CAACC,QAAQ;8BACb,cAAA,qBAACC;wBAAEC,MAAML;kCAAOA;;mBADG,AAAC,UAAOC;YAIjC;YACA,qBAAO,qBAACC,cAAK,CAACC,QAAQ;0BAAwBH;eAAlB,AAAC,UAAOC;QACtC,KACAN;;AAGV"}