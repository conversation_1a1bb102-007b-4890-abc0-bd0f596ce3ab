{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/internal/container/BuildError.tsx"], "names": ["BuildError", "styles", "message", "versionInfo", "noop", "React", "useCallback", "Overlay", "fixed", "Dialog", "type", "aria-<PERSON>by", "aria-<PERSON><PERSON>", "onClose", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogHeader", "className", "h4", "id", "VersionStalenessInfo", "DialogBody", "Terminal", "content", "footer", "p", "small", "css"], "mappings": ";;;;;;;;;;;;;;;IAeaA,UAAU;eAAVA;;IAmCAC,MAAM;eAANA;;;;;;iEAlDU;wBAOhB;yBACiB;0BACC;sCACY;8BACT;;;;;;;;;;AAIrB,MAAMD,aAAwC,SAASA,WAAW,KAGxE;IAHwE,IAAA,EACvEE,OAAO,EACPC,WAAW,EACZ,GAHwE;IAIvE,MAAMC,OAAOC,OAAMC,WAAW,CAAC,KAAO,GAAG,EAAE;IAC3C,qBACE,qBAACC,gBAAO;QAACC,KAAK;kBACZ,cAAA,qBAACC,cAAM;YACLC,MAAK;YACLC,mBAAgB;YAChBC,oBAAiB;YACjBC,SAAST;sBAET,cAAA,sBAACU,qBAAa;;kCACZ,sBAACC,oBAAY;wBAACC,WAAU;;0CACtB,qBAACC;gCAAGC,IAAG;0CAAsC;;4BAC5Cf,4BAAc,qBAACgB,0CAAoB;gCAAE,GAAGhB,WAAW;iCAAO;;;kCAE7D,sBAACiB,kBAAU;wBAACJ,WAAU;;0CACpB,qBAACK,kBAAQ;gCAACC,SAASpB;;0CACnB,qBAACqB;0CACC,cAAA,qBAACC;oCAAEN,IAAG;8CACJ,cAAA,qBAACO;kDAAM;;;;;;;;;;AAWvB;AAEO,MAAMxB,aAASyB,kBAAG"}