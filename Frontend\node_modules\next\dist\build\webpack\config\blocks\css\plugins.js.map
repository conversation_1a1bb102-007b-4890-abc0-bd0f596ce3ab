{"version": 3, "sources": ["../../../../../../src/build/webpack/config/blocks/css/plugins.ts"], "names": ["getPostCssPlugins", "genericErrorText", "getError_NullConfig", "pluginName", "red", "bold", "isIgnoredPlugin", "pluginPath", "ignoredRegex", "match", "exec", "plugin", "pop", "console", "warn", "yellow", "underline", "createLazyPostCssPlugin", "fn", "result", "undefined", "args", "postcss", "loadPlugin", "dir", "options", "error", "Error", "require", "resolve", "paths", "Object", "keys", "length", "getDefaultPlugins", "supportedBrowsers", "disablePostcssPresetEnv", "browsers", "autoprefixer", "flexbox", "stage", "features", "filter", "Boolean", "config", "findConfig", "plugins", "<PERSON><PERSON><PERSON>", "find", "key", "Array", "isArray", "pc", "reduce", "acc", "curr", "p", "push", "parsed", "for<PERSON>ach", "pluginConfig", "resolved", "Promise", "all", "map", "filtered"], "mappings": ";;;;+BAmHsBA;;;eAAAA;;;4BAnHuB;4BAClB;AAY3B,MAAMC,mBAAmB;AAEzB,SAASC,oBAAoBC,UAAkB;IAC7C,OAAO,CAAC,EAAEC,IAAAA,eAAG,EACXC,IAAAA,gBAAI,EAAC,UACL,kCAAkC,EAAEF,WAAW,cAAc,EAAEE,IAAAA,gBAAI,EACnE,QACA,6BAA6B,EAAEF,WAAW,QAAQ,EAAEE,IAAAA,gBAAI,EACxD,SACA,kBAAkB,EAAEA,IAAAA,gBAAI,EAAC,QAAQ,2BAA2B,CAAC;AACjE;AAEA,SAASC,gBAAgBC,UAAkB;IACzC,MAAMC,eACJ;IACF,MAAMC,QAAQD,aAAaE,IAAI,CAACH;IAChC,IAAIE,SAAS,MAAM;QACjB,OAAO;IACT;IAEA,MAAME,SAASF,MAAMG,GAAG;IACxBC,QAAQC,IAAI,CACV,CAAC,EAAEC,IAAAA,kBAAM,EAACV,IAAAA,gBAAI,EAAC,YAAY,oBAAoB,EAAEW,IAAAA,qBAAS,EACxDL,QACA,yCAAyC,CAAC,GAC1C,CAAC,qDAAqD,CAAC,GACvD;IAEJ,OAAO;AACT;AAEA,MAAMM,0BAA0B,CAC9BC;IAEA,IAAIC,SAAcC;IAClB,MAAMT,SAAS,CAAC,GAAGU;QACjB,IAAIF,WAAWC,WAAWD,SAASD;QACnC,IAAIC,OAAOG,OAAO,KAAK,MAAM;YAC3B,OAAOH,UAAUE;QACnB,OAAO,IAAIF,OAAOG,OAAO,EAAE;YACzB,OAAOH,OAAOG,OAAO;QACvB;QACA,OAAOH;IACT;IACAR,OAAOW,OAAO,GAAG;IACjB,OAAOX;AACT;AAEA,eAAeY,WACbC,GAAW,EACXrB,UAAkB,EAClBsB,OAAkC;IAElC,IAAIA,YAAY,SAASnB,gBAAgBH,aAAa;QACpD,OAAO;IACT;IAEA,IAAIsB,WAAW,MAAM;QACnBZ,QAAQa,KAAK,CAACxB,oBAAoBC;QAClC,MAAM,IAAIwB,MAAM1B;IAClB;IAEA,MAAMM,aAAaqB,QAAQC,OAAO,CAAC1B,YAAY;QAAE2B,OAAO;YAACN;SAAI;IAAC;IAC9D,IAAIlB,gBAAgBC,aAAa;QAC/B,OAAO;IACT,OAAO,IAAIkB,YAAY,MAAM;QAC3B,OAAOR,wBAAwB,IAAMW,QAAQrB;IAC/C,OAAO;QACL,IAAI,OAAOkB,YAAY,YAAYM,OAAOC,IAAI,CAACP,SAASQ,MAAM,KAAK,GAAG;YACpE,OAAOhB,wBAAwB,IAAMW,QAAQrB;QAC/C;QACA,OAAOU,wBAAwB,IAAMW,QAAQrB,YAAYkB;IAC3D;AACF;AAEA,SAASS,kBACPC,iBAAuC,EACvCC,uBAAgC;IAEhC,OAAO;QACLR,QAAQC,OAAO,CAAC;QAChBO,0BACI,QACA;YACER,QAAQC,OAAO,CAAC;YAChB;gBACEQ,UAAUF,qBAAqB;oBAAC;iBAAW;gBAC3CG,cAAc;oBACZ,iCAAiC;oBACjCC,SAAS;gBACX;gBACA,+CAA+C;gBAC/C,+CAA+C;gBAC/CC,OAAO;gBACPC,UAAU;oBACR,qBAAqB;gBACvB;YACF;SACD;KACN,CAACC,MAAM,CAACC;AACX;AAEO,eAAe3C,kBACpBwB,GAAW,EACXW,iBAAuC,EACvCC,0BAAmC,KAAK;IAExC,IAAIQ,SAAS,MAAMC,IAAAA,sBAAU,EAC3BrB,KACA;IAGF,IAAIoB,UAAU,MAAM;QAClBA,SAAS;YACPE,SAASZ,kBAAkBC,mBAAmBC;QAChD;IACF;IAEA,IAAI,OAAOQ,WAAW,YAAY;QAChC,MAAM,IAAIjB,MACR,CAAC,oGAAoG,CAAC,GACpG;IAEN;IAEA,6DAA6D;IAC7D,MAAMoB,aAAahB,OAAOC,IAAI,CAACY,QAAQI,IAAI,CAAC,CAACC,MAAQA,QAAQ;IAC7D,IAAIF,YAAY;QACdlC,QAAQC,IAAI,CACV,CAAC,EAAEC,IAAAA,kBAAM,EACPV,IAAAA,gBAAI,EAAC,YACL,uEAAuE,EAAE0C,WAAW,KAAK,CAAC,GAC1F,CAAC,uCAAuC,CAAC;IAE/C;IAEA,yEAAyE;IACzE,IAAID,UAAUF,OAAOE,OAAO;IAC5B,IAAIA,WAAW,QAAQ,OAAOA,YAAY,UAAU;QAClD,MAAM,IAAInB,MACR,CAAC,gEAAgE,CAAC;IAEtE;IAEA,IAAI,CAACuB,MAAMC,OAAO,CAACL,UAAU;QAC3B,0CAA0C;QAC1C,MAAMM,KAAKN;QAEXA,UAAUf,OAAOC,IAAI,CAACc,SAASO,MAAM,CAAC,CAACC,KAAKC;YAC1C,MAAMC,IAAIJ,EAAE,CAACG,KAAK;YAClB,IAAI,OAAOC,MAAM,aAAa;gBAC5B3C,QAAQa,KAAK,CAACxB,oBAAoBqD;gBAClC,MAAM,IAAI5B,MAAM1B;YAClB;YAEAqD,IAAIG,IAAI,CAAC;gBAACF;gBAAMC;aAAE;YAClB,OAAOF;QACT,GAAG,EAAE;IACP;IAEA,MAAMI,SAA2B,EAAE;IACnCZ,QAAQa,OAAO,CAAC,CAAChD;QACf,IAAIA,UAAU,MAAM;YAClBE,QAAQC,IAAI,CACV,CAAC,EAAEC,IAAAA,kBAAM,EAACV,IAAAA,gBAAI,EAAC,YAAY,IAAI,EAAEA,IAAAA,gBAAI,EACnC,QACA,yDAAyD,CAAC;QAEhE,OAAO,IAAI,OAAOM,WAAW,UAAU;YACrC+C,OAAOD,IAAI,CAAC;gBAAC9C;gBAAQ;aAAK;QAC5B,OAAO,IAAIuC,MAAMC,OAAO,CAACxC,SAAS;YAChC,MAAMR,aAAaQ,MAAM,CAAC,EAAE;YAC5B,MAAMiD,eAAejD,MAAM,CAAC,EAAE;YAC9B,IACE,OAAOR,eAAe,YACrB,CAAA,OAAOyD,iBAAiB,aACvB,OAAOA,iBAAiB,YACxB,OAAOA,iBAAiB,QAAO,GACjC;gBACAF,OAAOD,IAAI,CAAC;oBAACtD;oBAAYyD;iBAAa;YACxC,OAAO;gBACL,IAAI,OAAOzD,eAAe,UAAU;oBAClCU,QAAQa,KAAK,CACX,CAAC,EAAEtB,IAAAA,eAAG,EACJC,IAAAA,gBAAI,EAAC,UACL,yCAAyC,EAAEA,IAAAA,gBAAI,EAC/C,UACA,oBAAoB,EAAEF,WAAW,IAAI,CAAC,GACtC;gBAEN,OAAO;oBACLU,QAAQa,KAAK,CACX,CAAC,EAAEtB,IAAAA,eAAG,EACJC,IAAAA,gBAAI,EAAC,UACL,kFAAkF,EAAEF,WAAW,KAAK,CAAC,GACrG;gBAEN;gBACA,MAAM,IAAIwB,MAAM1B;YAClB;QACF,OAAO,IAAI,OAAOU,WAAW,YAAY;YACvCE,QAAQa,KAAK,CACX,CAAC,EAAEtB,IAAAA,eAAG,EACJC,IAAAA,gBAAI,EAAC,UACL,0FAA0F,EAAEA,IAAAA,gBAAI,EAChG,UACA,4DAA4D,CAAC;YAEjE,MAAM,IAAIsB,MAAM1B;QAClB,OAAO;YACLY,QAAQa,KAAK,CACX,CAAC,EAAEtB,IAAAA,eAAG,EACJC,IAAAA,gBAAI,EAAC,UACL,0CAA0C,EAAEM,OAAO,IAAI,CAAC,GACxD;YAEJ,MAAM,IAAIgB,MAAM1B;QAClB;IACF;IAEA,MAAM4D,WAAW,MAAMC,QAAQC,GAAG,CAChCL,OAAOM,GAAG,CAAC,CAACR,IAAMjC,WAAWC,KAAKgC,CAAC,CAAC,EAAE,EAAEA,CAAC,CAAC,EAAE;IAE9C,MAAMS,WAA+CJ,SAASnB,MAAM,CAClEC;IAGF,OAAOsB;AACT"}