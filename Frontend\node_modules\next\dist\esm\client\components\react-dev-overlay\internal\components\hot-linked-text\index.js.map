{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/internal/components/hot-linked-text/index.tsx"], "names": ["React", "getWordsAndWhitespaces", "linkRegex", "HotlinkedText", "props", "text", "wordsAndWhitespaces", "test", "map", "word", "index", "Fragment", "a", "href"], "mappings": ";AAAA,OAAOA,WAAW,QAAO;AACzB,SAASC,sBAAsB,QAAQ,8BAA6B;AAEpE,MAAMC,YAAY;AAElB,OAAO,MAAMC,gBAER,SAASA,cAAcC,KAAK;IAC/B,MAAM,EAAEC,IAAI,EAAE,GAAGD;IAEjB,MAAME,sBAAsBL,uBAAuBI;IAEnD,qBACE;kBACGH,UAAUK,IAAI,CAACF,QACZC,oBAAoBE,GAAG,CAAC,CAACC,MAAMC;YAC7B,IAAIR,UAAUK,IAAI,CAACE,OAAO;gBACxB,qBACE,KAACT,MAAMW,QAAQ;8BACb,cAAA,KAACC;wBAAEC,MAAMJ;kCAAOA;;mBADG,AAAC,UAAOC;YAIjC;YACA,qBAAO,KAACV,MAAMW,QAAQ;0BAAwBF;eAAlB,AAAC,UAAOC;QACtC,KACAL;;AAGV,EAAC"}