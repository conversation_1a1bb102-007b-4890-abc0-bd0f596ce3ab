{"version": 3, "sources": ["../../../../../../src/build/webpack/loaders/resolve-url-loader/lib/join-function.ts"], "names": ["path", "fs", "compose", "f", "g", "args", "simpleJoin", "normalize", "join", "defaultJoin", "createJoinForPredicate", "predicate", "_", "uri", "base", "i", "next", "absolute", "existsSync", "createIterator", "arr", "name", "filename", "options", "log", "createDebugLogger", "debug", "joinProper", "baseOrIteratorOrAbsent", "iterator", "root", "result", "runIterator", "createJoin<PERSON>g", "isFound", "accumulator", "nextItem", "done", "value", "element", "length", "isAbsolute", "Object", "assign", "concat", "Array", "isArray", "Error", "fallback", "toString", "valueOf", "file", "bases", "pathToString", "map", "filter", "Boolean", "relative", "process", "cwd", "split", "sep", "exports", "console", "cache", "<PERSON><PERSON><PERSON>", "noop", "msgFn", "params", "key", "JSON", "stringify", "apply"], "mappings": "AAAA,uEAAuE;AACvE,0DAA0D,GAC1D;;;;;;;;;;;;;;;;;;;;;;AAsBA,GACA,OAAOA,UAAU,OAAM;AACvB,OAAOC,QAAQ,KAAI;AAEnB,MAAMC,UACJ,CAACC,GAAQC,IACT,CAAC,GAAGC,OACFF,EAAEC,KAAKC;AAEX,MAAMC,aAAaJ,QAAQF,KAAKO,SAAS,EAAEP,KAAKQ,IAAI;AAEpD;;;;;;CAMC,GACD,OAAO,MAAMC,cAAcC,uBAAuB,SAASC,UACzDC,CAAM,EACNC,GAAQ,EACRC,IAAS,EACTC,CAAM,EACNC,IAAS;IAET,MAAMC,WAAWX,WAAWQ,MAAMD;IAClC,OAAOZ,GAAGiB,UAAU,CAACD,YAAYA,WAAWD,KAAKD,MAAM,IAAIE,WAAW;AACxE,GACA,eAAc;AAEd,UAAUE,eAAeC,GAAQ;IAC/B,KAAK,MAAML,KAAKK,IAAK;QACnB,MAAML;IACR;AACF;AAEA;;;;;;;;;;;;;;;;;;;;CAoBC,GACD,SAASL,uBACP,2CAA2C,GAC3CC,SAAc,EACd,kDAAkD,GAClDU,IAAY;IAEZ;;GAEC,GACD,SAASb,KACP,qCAAqC,GACrCc,QAAgB,EAChB,oBAAoB,GACpBC,OAAgD;QAEhD,MAAMC,MAAMC,kBAAkBF,QAAQG,KAAK;QAE3C;;;;;;KAMC,GACD,OAAO,SAASC,WACd,qCAAqC,GACrCd,GAAW,EACX,oDAAoD,GACpDe,sBAA2B;YAE3B,MAAMC,WACJ,AAAC,OAAOD,2BAA2B,eACjCT,eAAe;gBAACI,QAAQO,IAAI;aAAC,KAC9B,OAAOF,2BAA2B,YACjCT,eAAe;gBAACS;aAAuB,KACzCA;YAEF,MAAMG,SAASC,YAAY,EAAE;YAC7BR,IAAIS,eAAe;gBAACX;gBAAUT;gBAAKkB;gBAAQA,OAAOG,OAAO;aAAC;YAE1D,OAAO,OAAOH,OAAOd,QAAQ,KAAK,WAAWc,OAAOd,QAAQ,GAAGJ;YAE/D,SAASmB,YAAYG,WAAgB;gBACnC,MAAMC,WAAWP,SAASb,IAAI;gBAC9B,IAAIF,OAAO,CAACsB,SAASC,IAAI,IAAID,SAASE,KAAK;gBAC3C,IAAI,OAAOxB,SAAS,UAAU;oBAC5B,MAAMyB,UAAU5B,UACdW,UACAT,KACAC,MACAqB,YAAYK,MAAM,EAClBxB;oBAGF,IAAI,OAAOuB,YAAY,YAAYvC,KAAKyC,UAAU,CAACF,UAAU;wBAC3D,OAAOG,OAAOC,MAAM,CAACR,YAAYS,MAAM,CAAC9B,OAAO;4BAC7CoB,SAAS;4BACTjB,UAAUsB;wBACZ;oBACF,OAAO,IAAIM,MAAMC,OAAO,CAACP,UAAU;wBACjC,OAAOA;oBACT,OAAO;wBACL,MAAM,IAAIQ,MACR;oBAEJ;gBACF,OAAO;oBACL,OAAOZ;gBACT;gBAEA,SAASnB,KAAKgC,QAAa;oBACzB,OAAOhB,YACLU,OAAOC,MAAM,CACXR,YAAYS,MAAM,CAAC9B,OACnB,OAAOkC,aAAa,YAAY;wBAAE/B,UAAU+B;oBAAS;gBAG3D;YACF;QACF;IACF;IAEA,SAASC;QACP,OAAO,gBAAgB5B,OAAO;IAChC;IAEA,OAAOqB,OAAOC,MAAM,CAClBnC,MACAa,QAAQ;QACN6B,SAASD;QACTA,UAAUA;IACZ;AAEJ;AAEA;;;CAGC,GACD,SAAShB,cACP,wCAAwC,GACxCkB,IAAY,EACZ,sCAAsC,GACtCtC,GAAW,EACX,0DAA0D,GAC1DuC,KAAe,EACf,wCAAwC,GACxClB,OAAgB;IAEhB,OAAO;QACL,yBAAyBmB,aAAaF,QAAQ,OAAOtC;QACrD,EAAE;WACCuC,MAAME,GAAG,CAACD,cAAcE,MAAM,CAACC;WAC9BtB,UAAU;YAAC;SAAQ,GAAG;YAAC;SAAY;KACxC,CAAC1B,IAAI,CAAC;IAEP;;;;GAIC,GACD,SAAS6C,aACP,qBAAqB,GACrBpC,QAAgB;QAEhB,IAAI,CAACA,UAAU;YACb,OAAO;QACT,OAAO;YACL,MAAMwC,WAAWzD,KAAKyD,QAAQ,CAACC,QAAQC,GAAG,IAAI1C,UAAU2C,KAAK,CAAC5D,KAAK6D,GAAG;YAEtE,OAAO,AACLJ,CAAAA,QAAQ,CAAC,EAAE,KAAK,OACZxC,SAAS2C,KAAK,CAAC5D,KAAK6D,GAAG,IACvB;gBAAC;aAAI,CAACjB,MAAM,CAACa,UAAUF,MAAM,CAACC,QAAO,EACzChD,IAAI,CAAC;QACT;IACF;AACF;AAEAsD,QAAQ7B,aAAa,GAAGA;AAExB;;;;;;;;;;CAUC,GACD,SAASR,kBACP,gCAAgC,GAChCC,KAAoB;IAEpB,MAAMF,MAAM,CAAC,CAACE,SAAU,CAAA,OAAOA,UAAU,aAAaA,QAAQqC,QAAQvC,GAAG,AAAD;IACxE,MAAMwC,QAAa,CAAC;IACpB,OAAOxC,MAAMyC,cAAcC;IAE3B,SAASA,QAAQ;IAEjB,SAASD,YAAYE,KAAU,EAAEC,MAAW;QAC1C,MAAMC,MAAMC,KAAKC,SAAS,CAACH;QAC3B,IAAI,CAACJ,KAAK,CAACK,IAAI,EAAE;YACfL,KAAK,CAACK,IAAI,GAAG;YACb7C,IAAI2C,MAAMK,KAAK,CAAC,MAAMJ;QACxB;IACF;AACF;AAEAN,QAAQrC,iBAAiB,GAAGA"}