{"version": 3, "sources": ["../../../../src/shared/lib/router/action-queue.ts"], "names": ["isThenable", "ACTION_REFRESH", "ACTION_SERVER_ACTION", "ACTION_NAVIGATE", "ACTION_RESTORE", "reducer", "React", "startTransition", "ActionQueueContext", "createContext", "runRemainingActions", "actionQueue", "setState", "pending", "next", "runAction", "action", "prevState", "state", "Error", "payload", "actionResult", "handleResult", "nextState", "discarded", "needsRefresh", "dispatch", "type", "origin", "window", "location", "devToolsInstance", "send", "resolve", "then", "err", "reject", "dispatchAction", "resolvers", "deferred<PERSON><PERSON><PERSON>", "Promise", "newAction", "last", "createMutableActionQueue", "result"], "mappings": "AAAA,SACEA,UAAU,EAIVC,cAAc,EACdC,oBAAoB,EACpBC,eAAe,EACfC,cAAc,QACT,iEAAgE;AAEvE,SAASC,OAAO,QAAQ,2DAA0D;AAClF,OAAOC,SAASC,eAAe,QAAQ,QAAO;AAsB9C,OAAO,MAAMC,qBACXF,MAAMG,aAAa,CAA8B,MAAK;AAExD,SAASC,oBACPC,WAAiC,EACjCC,QAA8B;IAE9B,IAAID,YAAYE,OAAO,KAAK,MAAM;QAChCF,YAAYE,OAAO,GAAGF,YAAYE,OAAO,CAACC,IAAI;QAC9C,IAAIH,YAAYE,OAAO,KAAK,MAAM;YAChC,mEAAmE;YACnEE,UAAU;gBACRJ;gBACAK,QAAQL,YAAYE,OAAO;gBAC3BD;YACF;QACF;IACF;AACF;AAEA,eAAeG,UAAU,KAQxB;IARwB,IAAA,EACvBJ,WAAW,EACXK,MAAM,EACNJ,QAAQ,EAKT,GARwB;IASvB,MAAMK,YAAYN,YAAYO,KAAK;IACnC,IAAI,CAACD,WAAW;QACd,sFAAsF;QACtF,MAAM,IAAIE,MAAM;IAClB;IAEAR,YAAYE,OAAO,GAAGG;IAEtB,MAAMI,UAAUJ,OAAOI,OAAO;IAC9B,MAAMC,eAAeV,YAAYK,MAAM,CAACC,WAAWG;IAEnD,SAASE,aAAaC,SAAyB;QAC7C,kEAAkE;QAClE,IAAIP,OAAOQ,SAAS,EAAE;YACpB,oFAAoF;YACpF,IAAIb,YAAYc,YAAY,IAAId,YAAYE,OAAO,KAAK,MAAM;gBAC5DF,YAAYc,YAAY,GAAG;gBAC3Bd,YAAYe,QAAQ,CAClB;oBACEC,MAAM1B;oBACN2B,QAAQC,OAAOC,QAAQ,CAACF,MAAM;gBAChC,GACAhB;YAEJ;YACA;QACF;QAEAD,YAAYO,KAAK,GAAGK;QAEpB,IAAIZ,YAAYoB,gBAAgB,EAAE;YAChCpB,YAAYoB,gBAAgB,CAACC,IAAI,CAACZ,SAASG;QAC7C;QAEAb,oBAAoBC,aAAaC;QACjCI,OAAOiB,OAAO,CAACV;IACjB;IAEA,8DAA8D;IAC9D,IAAIvB,WAAWqB,eAAe;QAC5BA,aAAaa,IAAI,CAACZ,cAAc,CAACa;YAC/BzB,oBAAoBC,aAAaC;YACjCI,OAAOoB,MAAM,CAACD;QAChB;IACF,OAAO;QACLb,aAAaD;IACf;AACF;AAEA,SAASgB,eACP1B,WAAiC,EACjCS,OAAuB,EACvBR,QAA8B;IAE9B,IAAI0B,YAGA;QAAEL,SAASrB;QAAUwB,QAAQ,KAAO;IAAE;IAE1C,mEAAmE;IACnE,wFAAwF;IACxF,2DAA2D;IAC3D,oDAAoD;IACpD,IAAIhB,QAAQO,IAAI,KAAKvB,gBAAgB;QACnC,6DAA6D;QAC7D,MAAMmC,kBAAkB,IAAIC,QAAwB,CAACP,SAASG;YAC5DE,YAAY;gBAAEL;gBAASG;YAAO;QAChC;QAEA7B,gBAAgB;YACd,oGAAoG;YACpG,iEAAiE;YACjEK,SAAS2B;QACX;IACF;IAEA,MAAME,YAA6B;QACjCrB;QACAN,MAAM;QACNmB,SAASK,UAAWL,OAAO;QAC3BG,QAAQE,UAAWF,MAAM;IAC3B;IAEA,8BAA8B;IAC9B,IAAIzB,YAAYE,OAAO,KAAK,MAAM;QAChC,iEAAiE;QACjE,4CAA4C;QAC5CF,YAAY+B,IAAI,GAAGD;QAEnB1B,UAAU;YACRJ;YACAK,QAAQyB;YACR7B;QACF;IACF,OAAO,IAAIQ,QAAQO,IAAI,KAAKxB,iBAAiB;QAC3C,sDAAsD;QACtD,oHAAoH;QACpHQ,YAAYE,OAAO,CAACW,SAAS,GAAG;QAEhC,4CAA4C;QAC5Cb,YAAY+B,IAAI,GAAGD;QAEnB,2GAA2G;QAC3G,IAAI9B,YAAYE,OAAO,CAACO,OAAO,CAACO,IAAI,KAAKzB,sBAAsB;YAC7DS,YAAYc,YAAY,GAAG;QAC7B;QAEAV,UAAU;YACRJ;YACAK,QAAQyB;YACR7B;QACF;IACF,OAAO;QACL,oEAAoE;QACpE,+EAA+E;QAC/E,IAAID,YAAY+B,IAAI,KAAK,MAAM;YAC7B/B,YAAY+B,IAAI,CAAC5B,IAAI,GAAG2B;QAC1B;QACA9B,YAAY+B,IAAI,GAAGD;IACrB;AACF;AAEA,OAAO,SAASE;IACd,MAAMhC,cAAoC;QACxCO,OAAO;QACPQ,UAAU,CAACN,SAAyBR,WAClCyB,eAAe1B,aAAaS,SAASR;QACvCI,QAAQ,OAAOE,OAAuBF;YACpC,IAAIE,UAAU,MAAM;gBAClB,MAAM,IAAIC,MAAM;YAClB;YACA,MAAMyB,SAASvC,QAAQa,OAAOF;YAC9B,OAAO4B;QACT;QACA/B,SAAS;QACT6B,MAAM;IACR;IAEA,OAAO/B;AACT"}