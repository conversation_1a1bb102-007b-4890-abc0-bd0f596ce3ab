{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/internal/components/CodeFrame/CodeFrame.tsx"], "names": ["CodeFrame", "stackFrame", "codeFrame", "formattedFrame", "React", "useMemo", "lines", "split", "prefixLength", "map", "line", "exec", "stripAnsi", "filter", "Boolean", "v", "pop", "reduce", "c", "n", "isNaN", "length", "Math", "min", "NaN", "p", "repeat", "a", "indexOf", "substring", "replace", "join", "decoded", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "json", "use_classes", "remove_empty", "open", "useOpenInEditor", "file", "lineNumber", "column", "div", "data-nextjs-codeframe", "role", "onClick", "tabIndex", "title", "span", "getFrameSource", "methodName", "svg", "xmlns", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "path", "d", "polyline", "points", "x1", "y1", "x2", "y2", "pre", "entry", "index", "style", "color", "fg", "undefined", "decoration", "fontWeight", "fontStyle", "content"], "mappings": ";;;;+BASaA;;;eAAAA;;;;;;gEATK;iEACK;oEAED;4BACS;iCACC;AAIzB,MAAMA,YAAsC,SAASA,UAAU,KAGrE;IAHqE,IAAA,EACpEC,UAAU,EACVC,SAAS,EACV,GAHqE;IAIpE,8CAA8C;IAC9C,MAAMC,iBAAiBC,OAAMC,OAAO,CAAS;QAC3C,MAAMC,QAAQJ,UAAUK,KAAK,CAAC;QAC9B,MAAMC,eAAeF,MAClBG,GAAG,CAAC,CAACC,OACJ,oBAAoBC,IAAI,CAACC,IAAAA,kBAAS,EAACF,WAAW,OAC1C,OACA,oBAAoBC,IAAI,CAACC,IAAAA,kBAAS,EAACF,QAExCG,MAAM,CAACC,SACPL,GAAG,CAAC,CAACM,IAAMA,EAAGC,GAAG,IACjBC,MAAM,CAAC,CAACC,GAAGC,IAAOC,MAAMF,KAAKC,EAAEE,MAAM,GAAGC,KAAKC,GAAG,CAACL,GAAGC,EAAEE,MAAM,GAAIG;QAEnE,IAAIhB,eAAe,GAAG;YACpB,MAAMiB,IAAI,IAAIC,MAAM,CAAClB;YACrB,OAAOF,MACJG,GAAG,CAAC,CAACC,MAAMiB,IACV,CAAEA,CAAAA,IAAIjB,KAAKkB,OAAO,CAAC,IAAG,IAClBlB,KAAKmB,SAAS,CAAC,GAAGF,KAAKjB,KAAKmB,SAAS,CAACF,GAAGG,OAAO,CAACL,GAAG,MACpDf,MAELqB,IAAI,CAAC;QACV;QACA,OAAOzB,MAAMyB,IAAI,CAAC;IACpB,GAAG;QAAC7B;KAAU;IAEd,MAAM8B,UAAU5B,OAAMC,OAAO,CAAC;QAC5B,OAAO4B,cAAK,CAACC,UAAU,CAAC/B,gBAAgB;YACtCgC,MAAM;YACNC,aAAa;YACbC,cAAc;QAChB;IACF,GAAG;QAAClC;KAAe;IAEnB,MAAMmC,OAAOC,IAAAA,gCAAe,EAAC;QAC3BC,MAAMvC,WAAWuC,IAAI;QACrBC,YAAYxC,WAAWwC,UAAU;QACjCC,QAAQzC,WAAWyC,MAAM;IAC3B;IAEA,gCAAgC;IAChC,qBACE,sBAACC;QAAIC,uBAAqB;;0BACxB,qBAACD;0BACC,cAAA,sBAAClB;oBACCoB,MAAK;oBACLC,SAASR;oBACTS,UAAU;oBACVC,OAAM;;sCAEN,sBAACC;;gCACEC,IAAAA,0BAAc,EAACjD;gCAAY;gCAAIA,WAAWkD,UAAU;;;sCAEvD,sBAACC;4BACCC,OAAM;4BACNC,SAAQ;4BACRC,MAAK;4BACLC,QAAO;4BACPC,aAAY;4BACZC,eAAc;4BACdC,gBAAe;;8CAEf,qBAACC;oCAAKC,GAAE;;8CACR,qBAACC;oCAASC,QAAO;;8CACjB,qBAACrD;oCAAKsD,IAAG;oCAAKC,IAAG;oCAAKC,IAAG;oCAAKC,IAAG;;;;;;;0BAIvC,qBAACC;0BACEpC,QAAQvB,GAAG,CAAC,CAAC4D,OAAOC,sBACnB,qBAACrB;wBAECsB,OAAO;4BACLC,OAAOH,MAAMI,EAAE,GAAG,AAAC,iBAAcJ,MAAMI,EAAE,GAAC,MAAKC;4BAC/C,GAAIL,MAAMM,UAAU,KAAK,SACrB;gCAAEC,YAAY;4BAAI,IAClBP,MAAMM,UAAU,KAAK,WACrB;gCAAEE,WAAW;4BAAS,IACtBH,SAAS;wBACf;kCAECL,MAAMS,OAAO;uBAVT,AAAC,WAAQR;;;;AAgB1B"}