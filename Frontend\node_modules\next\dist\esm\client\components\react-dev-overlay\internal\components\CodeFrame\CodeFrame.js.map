{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/internal/components/CodeFrame/CodeFrame.tsx"], "names": ["<PERSON><PERSON>", "React", "stripAnsi", "getFrameSource", "useOpenInEditor", "CodeFrame", "stackFrame", "codeFrame", "formattedFrame", "useMemo", "lines", "split", "prefixLength", "map", "line", "exec", "filter", "Boolean", "v", "pop", "reduce", "c", "n", "isNaN", "length", "Math", "min", "NaN", "p", "repeat", "a", "indexOf", "substring", "replace", "join", "decoded", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "json", "use_classes", "remove_empty", "open", "file", "lineNumber", "column", "div", "data-nextjs-codeframe", "role", "onClick", "tabIndex", "title", "span", "methodName", "svg", "xmlns", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "path", "d", "polyline", "points", "x1", "y1", "x2", "y2", "pre", "entry", "index", "style", "color", "fg", "undefined", "decoration", "fontWeight", "fontStyle", "content"], "mappings": ";AAAA,OAAOA,WAAW,2BAA0B;AAC5C,YAAYC,WAAW,QAAO;AAE9B,OAAOC,eAAe,gCAA+B;AACrD,SAASC,cAAc,QAAQ,4BAA2B;AAC1D,SAASC,eAAe,QAAQ,mCAAkC;AAIlE,OAAO,MAAMC,YAAsC,SAASA,UAAU,KAGrE;IAHqE,IAAA,EACpEC,UAAU,EACVC,SAAS,EACV,GAHqE;IAIpE,8CAA8C;IAC9C,MAAMC,iBAAiBP,MAAMQ,OAAO,CAAS;QAC3C,MAAMC,QAAQH,UAAUI,KAAK,CAAC;QAC9B,MAAMC,eAAeF,MAClBG,GAAG,CAAC,CAACC,OACJ,oBAAoBC,IAAI,CAACb,UAAUY,WAAW,OAC1C,OACA,oBAAoBC,IAAI,CAACb,UAAUY,QAExCE,MAAM,CAACC,SACPJ,GAAG,CAAC,CAACK,IAAMA,EAAGC,GAAG,IACjBC,MAAM,CAAC,CAACC,GAAGC,IAAOC,MAAMF,KAAKC,EAAEE,MAAM,GAAGC,KAAKC,GAAG,CAACL,GAAGC,EAAEE,MAAM,GAAIG;QAEnE,IAAIf,eAAe,GAAG;YACpB,MAAMgB,IAAI,IAAIC,MAAM,CAACjB;YACrB,OAAOF,MACJG,GAAG,CAAC,CAACC,MAAMgB,IACV,CAAEA,CAAAA,IAAIhB,KAAKiB,OAAO,CAAC,IAAG,IAClBjB,KAAKkB,SAAS,CAAC,GAAGF,KAAKhB,KAAKkB,SAAS,CAACF,GAAGG,OAAO,CAACL,GAAG,MACpDd,MAELoB,IAAI,CAAC;QACV;QACA,OAAOxB,MAAMwB,IAAI,CAAC;IACpB,GAAG;QAAC3B;KAAU;IAEd,MAAM4B,UAAUlC,MAAMQ,OAAO,CAAC;QAC5B,OAAOT,MAAMoC,UAAU,CAAC5B,gBAAgB;YACtC6B,MAAM;YACNC,aAAa;YACbC,cAAc;QAChB;IACF,GAAG;QAAC/B;KAAe;IAEnB,MAAMgC,OAAOpC,gBAAgB;QAC3BqC,MAAMnC,WAAWmC,IAAI;QACrBC,YAAYpC,WAAWoC,UAAU;QACjCC,QAAQrC,WAAWqC,MAAM;IAC3B;IAEA,gCAAgC;IAChC,qBACE,MAACC;QAAIC,uBAAqB;;0BACxB,KAACD;0BACC,cAAA,MAAChB;oBACCkB,MAAK;oBACLC,SAASP;oBACTQ,UAAU;oBACVC,OAAM;;sCAEN,MAACC;;gCACE/C,eAAeG;gCAAY;gCAAIA,WAAW6C,UAAU;;;sCAEvD,MAACC;4BACCC,OAAM;4BACNC,SAAQ;4BACRC,MAAK;4BACLC,QAAO;4BACPC,aAAY;4BACZC,eAAc;4BACdC,gBAAe;;8CAEf,KAACC;oCAAKC,GAAE;;8CACR,KAACC;oCAASC,QAAO;;8CACjB,KAACjD;oCAAKkD,IAAG;oCAAKC,IAAG;oCAAKC,IAAG;oCAAKC,IAAG;;;;;;;0BAIvC,KAACC;0BACEjC,QAAQtB,GAAG,CAAC,CAACwD,OAAOC,sBACnB,KAACpB;wBAECqB,OAAO;4BACLC,OAAOH,MAAMI,EAAE,GAAG,AAAC,iBAAcJ,MAAMI,EAAE,GAAC,MAAKC;4BAC/C,GAAIL,MAAMM,UAAU,KAAK,SACrB;gCAAEC,YAAY;4BAAI,IAClBP,MAAMM,UAAU,KAAK,WACrB;gCAAEE,WAAW;4BAAS,IACtBH,SAAS;wBACf;kCAECL,MAAMS,OAAO;uBAVT,AAAC,WAAQR;;;;AAgB1B,EAAC"}