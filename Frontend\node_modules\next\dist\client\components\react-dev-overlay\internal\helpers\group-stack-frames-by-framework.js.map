{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/internal/helpers/group-stack-frames-by-framework.ts"], "names": ["groupStackFramesByFramework", "getFramework", "sourcePackage", "undefined", "test", "stackFrames", "stackFramesGroupedByFramework", "stackFrame", "currentGroup", "length", "framework", "push"], "mappings": ";;;;+BAiDgBA;;;eAAAA;;;AA1ChB;;CAEC,GACD,SAASC,aACPC,aAAiC;IAEjC,IAAI,CAACA,eAAe,OAAOC;IAE3B,IACE,2GAA2GC,IAAI,CAC7GF,gBAEF;QACA,OAAO;IACT,OAAO,IAAIA,kBAAkB,QAAQ;QACnC,OAAO;IACT;IAEA,OAAOC;AACT;AAuBO,SAASH,4BACdK,WAAiC;IAEjC,MAAMC,gCAAoD,EAAE;IAE5D,KAAK,MAAMC,cAAcF,YAAa;QACpC,MAAMG,eACJF,6BAA6B,CAACA,8BAA8BG,MAAM,GAAG,EAAE;QACzE,MAAMC,YAAYT,aAAaM,WAAWL,aAAa;QAEvD,IAAIM,gBAAgBA,aAAaE,SAAS,KAAKA,WAAW;YACxDF,aAAaH,WAAW,CAACM,IAAI,CAACJ;QAChC,OAAO;YACLD,8BAA8BK,IAAI,CAAC;gBACjCD,WAAWA;gBACXL,aAAa;oBAACE;iBAAW;YAC3B;QACF;IACF;IAEA,OAAOD;AACT"}