{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/internal/container/RuntimeError/GroupedStackFrames.tsx"], "names": ["GroupedStackFrames", "FrameworkGroup", "framework", "stackFrames", "all", "details", "data-nextjs-collapsed-call-stack-details", "summary", "tabIndex", "svg", "data-nextjs-call-stack-chevron-icon", "fill", "height", "width", "shapeRendering", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "viewBox", "path", "d", "FrameworkIcon", "map", "frame", "index", "CallStackFrame", "groupedStackFrames", "stackFramesGroup", "groupIndex", "frameIndex"], "mappings": ";;;;+BA6CgBA;;;eAAAA;;;;gCA5Ce;+BACD;AAE9B,SAASC,eAAe,KAQvB;IARuB,IAAA,EACtBC,SAAS,EACTC,WAAW,EACXC,GAAG,EAKJ,GARuB;IAStB,qBACE;kBACE,cAAA,sBAACC;YAAQC,0CAAwC;;8BAC/C,sBAACC;oBACCC,UAAU;;sCAEV,qBAACC;4BACCC,qCAAmC;4BACnCC,MAAK;4BACLC,QAAO;4BACPC,OAAM;4BACNC,gBAAe;4BACfC,QAAO;4BACPC,eAAc;4BACdC,gBAAe;4BACfC,aAAY;4BACZC,SAAQ;sCAER,cAAA,qBAACC;gCAAKC,GAAE;;;sCAEV,qBAACC,4BAAa;4BAACpB,WAAWA;;wBACzBA,cAAc,UAAU,UAAU;;;gBAGpCC,YAAYoB,GAAG,CAAC,CAACC,OAAOC,sBACvB,qBAACC,8BAAc;wBAAoCF,OAAOA;uBAArC,AAAC,gBAAaC,QAAM,MAAGrB;;;;AAKtD;AAEO,SAASJ,mBAAmB,KAMlC;IANkC,IAAA,EACjC2B,kBAAkB,EAClBvB,GAAG,EAIJ,GANkC;IAOjC,qBACE;kBACGuB,mBAAmBJ,GAAG,CAAC,CAACK,kBAAkBC;YACzC,oCAAoC;YACpC,IAAID,iBAAiB1B,SAAS,EAAE;gBAC9B,qBACE,qBAACD;oBAECC,WAAW0B,iBAAiB1B,SAAS;oBACrCC,aAAayB,iBAAiBzB,WAAW;oBACzCC,KAAKA;mBAHA,AAAC,gCAA6ByB,aAAW,MAAGzB;YAMvD;YAEA,OACE,2CAA2C;YAC3CwB,iBAAiBzB,WAAW,CAACoB,GAAG,CAAC,CAACC,OAAOM,2BACvC,qBAACJ,8BAAc;oBAEbF,OAAOA;mBADF,AAAC,gBAAaK,aAAW,MAAGC,aAAW,MAAG1B;QAKvD;;AAGN"}