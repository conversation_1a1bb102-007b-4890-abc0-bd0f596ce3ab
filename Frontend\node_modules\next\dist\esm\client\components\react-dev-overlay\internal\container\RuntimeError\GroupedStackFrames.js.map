{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/internal/container/RuntimeError/GroupedStackFrames.tsx"], "names": ["CallStackFrame", "FrameworkIcon", "FrameworkGroup", "framework", "stackFrames", "all", "details", "data-nextjs-collapsed-call-stack-details", "summary", "tabIndex", "svg", "data-nextjs-call-stack-chevron-icon", "fill", "height", "width", "shapeRendering", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "viewBox", "path", "d", "map", "frame", "index", "GroupedStackFrames", "groupedStackFrames", "stackFramesGroup", "groupIndex", "frameIndex"], "mappings": ";AACA,SAASA,cAAc,QAAQ,mBAAkB;AACjD,SAASC,aAAa,QAAQ,kBAAiB;AAE/C,SAASC,eAAe,KAQvB;IARuB,IAAA,EACtBC,SAAS,EACTC,WAAW,EACXC,GAAG,EAKJ,GARuB;IAStB,qBACE;kBACE,cAAA,MAACC;YAAQC,0CAAwC;;8BAC/C,MAACC;oBACCC,UAAU;;sCAEV,KAACC;4BACCC,qCAAmC;4BACnCC,MAAK;4BACLC,QAAO;4BACPC,OAAM;4BACNC,gBAAe;4BACfC,QAAO;4BACPC,eAAc;4BACdC,gBAAe;4BACfC,aAAY;4BACZC,SAAQ;sCAER,cAAA,KAACC;gCAAKC,GAAE;;;sCAEV,KAACrB;4BAAcE,WAAWA;;wBACzBA,cAAc,UAAU,UAAU;;;gBAGpCC,YAAYmB,GAAG,CAAC,CAACC,OAAOC,sBACvB,KAACzB;wBAAkDwB,OAAOA;uBAArC,AAAC,gBAAaC,QAAM,MAAGpB;;;;AAKtD;AAEA,OAAO,SAASqB,mBAAmB,KAMlC;IANkC,IAAA,EACjCC,kBAAkB,EAClBtB,GAAG,EAIJ,GANkC;IAOjC,qBACE;kBACGsB,mBAAmBJ,GAAG,CAAC,CAACK,kBAAkBC;YACzC,oCAAoC;YACpC,IAAID,iBAAiBzB,SAAS,EAAE;gBAC9B,qBACE,KAACD;oBAECC,WAAWyB,iBAAiBzB,SAAS;oBACrCC,aAAawB,iBAAiBxB,WAAW;oBACzCC,KAAKA;mBAHA,AAAC,gCAA6BwB,aAAW,MAAGxB;YAMvD;YAEA,OACE,2CAA2C;YAC3CuB,iBAAiBxB,WAAW,CAACmB,GAAG,CAAC,CAACC,OAAOM,2BACvC,KAAC9B;oBAECwB,OAAOA;mBADF,AAAC,gBAAaK,aAAW,MAAGC,aAAW,MAAGzB;QAKvD;;AAGN"}