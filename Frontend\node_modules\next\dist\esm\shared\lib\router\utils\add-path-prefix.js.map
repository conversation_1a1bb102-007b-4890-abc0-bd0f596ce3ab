{"version": 3, "sources": ["../../../../../src/shared/lib/router/utils/add-path-prefix.ts"], "names": ["parsePath", "addPathPrefix", "path", "prefix", "startsWith", "pathname", "query", "hash"], "mappings": "AAAA,SAASA,SAAS,QAAQ,eAAc;AAExC;;;CAGC,GACD,OAAO,SAASC,cAAcC,IAAY,EAAEC,MAAe;IACzD,IAAI,CAACD,KAAKE,UAAU,CAAC,QAAQ,CAACD,QAAQ;QACpC,OAAOD;IACT;IAEA,MAAM,EAAEG,QAAQ,EAAEC,KAAK,EAAEC,IAAI,EAAE,GAAGP,UAAUE;IAC5C,OAAO,AAAC,KAAEC,SAASE,WAAWC,QAAQC;AACxC"}