{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/internal/components/ShadowPortal.tsx"], "names": ["React", "createPortal", "ShadowPort<PERSON>", "children", "portalNode", "useRef", "shadowNode", "forceUpdate", "useState", "useLayoutEffect", "ownerDocument", "document", "current", "createElement", "attachShadow", "mode", "body", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": "AAAA,YAAYA,WAAW,QAAO;AAC9B,SAASC,YAAY,QAAQ,YAAW;AAExC,OAAO,SAASC,aAAa,KAA2C;IAA3C,IAAA,EAAEC,QAAQ,EAAiC,GAA3C;IAC3B,IAAIC,aAAaJ,MAAMK,MAAM,CAAqB;IAClD,IAAIC,aAAaN,MAAMK,MAAM,CAAoB;IACjD,IAAI,GAAGE,YAAY,GAAGP,MAAMQ,QAAQ;IAEpCR,MAAMS,eAAe,CAAC;QACpB,MAAMC,gBAAgBC;QACtBP,WAAWQ,OAAO,GAAGF,cAAcG,aAAa,CAAC;QACjDP,WAAWM,OAAO,GAAGR,WAAWQ,OAAO,CAACE,YAAY,CAAC;YAAEC,MAAM;QAAO;QACpEL,cAAcM,IAAI,CAACC,WAAW,CAACb,WAAWQ,OAAO;QACjDL,YAAY,CAAC;QACb,OAAO;YACL,IAAIH,WAAWQ,OAAO,IAAIR,WAAWQ,OAAO,CAACF,aAAa,EAAE;gBAC1DN,WAAWQ,OAAO,CAACF,aAAa,CAACM,IAAI,CAACE,WAAW,CAACd,WAAWQ,OAAO;YACtE;QACF;IACF,GAAG,EAAE;IAEL,OAAON,WAAWM,OAAO,iBACrBX,aAAaE,UAAUG,WAAWM,OAAO,IACzC;AACN"}